package quote

import (
	"errors"
	"mime/multipart"
	"net/http"
	"strconv"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"pms-api/internal/domain/company"
	projectdomain "pms-api/internal/domain/project"
	quotedomain "pms-api/internal/domain/quote"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

type Handler interface {

	// GetQuote 獲取報價詳情
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 報價詳情
	//   - 400 Bad Request: 請求參數無效
	//   - 404 Not Found: 報價不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	GetQuote(c echo.Context) error

	// ListQuotes 獲取報價列表（舊版本，使用查詢參數）
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 報價列表及分頁資訊
	//   - 400 Bad Request: 請求參數無效
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	ListQuotes(c echo.Context) error

	// ListQuotesV2 獲取報價列表（新版本，使用POST請求體）
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 報價列表及分頁資訊
	//   - 400 Bad Request: 請求參數無效
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 403 Forbidden: 無權限訪問專案
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	ListQuotesV2(c echo.Context) error

	// CreateQuote 創建新報價
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 201 Created: 創建成功的報價詳情
	//   - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 404 Not Found: 專案或產品不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	CreateQuote(c echo.Context) error

	// UpdateQuote 更新報價
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 更新成功的報價詳情
	//   - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 404 Not Found: 報價不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	UpdateQuote(c echo.Context) error

	// DeleteQuote 刪除報價
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 刪除成功
	//   - 400 Bad Request: 請求參數無效
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 404 Not Found: 報價不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	DeleteQuote(c echo.Context) error

	// ApproveQuote 審核通過報價
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 審核通過成功
	//   - 400 Bad Request: 請求參數無效
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 403 Forbidden: 無權限執行此操作
	//   - 404 Not Found: 報價不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	ApproveQuote(c echo.Context) error

	// RejectQuote 審核退件報價
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 審核退件成功
	//   - 400 Bad Request: 請求參數無效
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 403 Forbidden: 無權限執行此操作
	//   - 404 Not Found: 報價不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	RejectQuote(c echo.Context) error

	// BatchApproveQuotes 批量審核通過報價
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 批量審核通過成功
	//   - 400 Bad Request: 請求參數無效
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 403 Forbidden: 無權限執行此操作
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	BatchApproveQuotes(c echo.Context) error

	// BatchRejectQuotes 批量審核退件報價
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 批量審核退件成功
	//   - 400 Bad Request: 請求參數無效
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 403 Forbidden: 無權限執行此操作
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	BatchRejectQuotes(c echo.Context) error

	// GetQuoteStats 獲取報價統計資訊
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 報價統計資訊
	//   - 400 Bad Request: 請求參數無效
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 404 Not Found: 專案不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	GetQuoteStats(c echo.Context) error
}

type handler struct {
	// quote 提供報價相關的業務邏輯
	quote quotedomain.Service

	// company 提供廠商相關的業務邏輯
	company companydomain.Service

	// project 提供專案相關的業務邏輯
	project projectdomain.Service

	// validator 用於驗證請求資料
	validator *validator.Validate

	// logger 用於記錄操作和錯誤
	logger *zap.Logger
}

// NewHandler 創建報價處理器的實例
func NewHandler(
	quote quotedomain.Service,
	company companydomain.Service,
	project projectdomain.Service,
	logger *zap.Logger,
) Handler {
	// 創建驗證器
	validate := validator.New()

	return &handler{
		quote:     quote,
		company:   company,
		project:   project,
		validator: validate,
		logger:    logger.Named("Handler").Named("Quote"),
	}
}

func (h *handler) GetQuote(c echo.Context) error {
	logger := h.logger.Named("GetQuote").With(zap.String("ip", c.RealIP()))
	logger.Info("獲取報價詳情請求開始處理")

	// 從路徑參數中獲取報價ID
	quoteIDStr := c.Param("id")
	if quoteIDStr == "" {
		logger.Error("報價ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "報價ID不可為空")
	}

	quoteID, err := strconv.ParseUint(quoteIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的報價ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的報價ID")
	}

	// 獲取報價詳情
	quoteDetail, err := h.quote.GetQuote(c.Request().Context(), uint32(quoteID))
	if err != nil {
		if errors.Is(err, quotedomain.ErrQuoteNotFound) {
			logger.Error("報價不存在", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價不存在")
		}
		if errors.Is(err, quotedomain.ErrQuoteAlreadyDeleted) {
			logger.Error("報價已刪除", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價已刪除")
		}
		logger.Error("獲取報價詳情失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "獲取報價詳情失敗")
	}

	logger.Info("獲取報價詳情成功", zap.Uint64("quoteID", quoteID))
	return c.JSON(http.StatusOK, DetailResponse{QuoteDetail: quoteDetail})
}

func (h *handler) ListQuotes(c echo.Context) error {
	logger := h.logger.Named("ListQuotes").With(zap.String("ip", c.RealIP()))
	logger.Info("獲取報價列表請求開始處理")

	// 解析請求參數
	var req ListQuoteRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("請求參數解析失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求參數: "+err.Error())
	}

	page, err := strconv.Atoi(c.QueryParam("page"))
	if err != nil {
		page = 1
	}
	pageSize, err := strconv.Atoi(c.QueryParam("size"))
	if err != nil {
		pageSize = 10
	}

	// 從上下文中獲取用戶ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 根據用戶角色決定查詢方式
	var quotes []*models.QuoteDetail
	var total int

	// 檢查專案ID是否提供
	if req.ProjectID == nil {
		logger.Error("專案ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "專案ID不可為空")
	}

	// 如果是廠商用戶，只能看到自己的報價
	if userRole == sqlc.UserRoleCompany {
		// 獲取廠商ID
		company, err := h.company.GetCompanyByUserID(c.Request().Context(), userID)
		if err != nil {
			logger.Error("獲取廠商資訊失敗", zap.Error(err))
			return echo.NewHTTPError(http.StatusInternalServerError, "獲取廠商資訊失敗")
		}

		// 查詢廠商報價
		quotes, total, err = h.quote.ListCompanyQuotes(c.Request().Context(), company.Company.ID, *req.ProjectID, page, pageSize)
		if err != nil {
			logger.Error("查詢廠商報價失敗", zap.Error(err))
			return echo.NewHTTPError(http.StatusInternalServerError, "查詢廠商報價失敗")
		}
		if len(quotes) == 0 {
			return echo.NewHTTPError(http.StatusNotFound, "找不到廠商報價")
		}
	} else {
		// 管理員可以看到所有報價
		quotes, total, err = h.quote.ListProjectQuotes(c.Request().Context(), *req.ProjectID, page, pageSize)
		if err != nil {
			logger.Error("查詢報價失敗", zap.Error(err))
			return echo.NewHTTPError(http.StatusInternalServerError, "查詢報價失敗")
		}
		if len(quotes) == 0 {
			return echo.NewHTTPError(http.StatusNotFound, "找不到報價")
		}
	}

	if err != nil {
		logger.Error("查詢報價列表失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "查詢報價列表失敗")
	}

	// 計算總頁數
	totalPages := (int64(total) + int64(pageSize) - 1) / int64(pageSize)

	// 構建回應
	response := ListResponse{
		Quotes:     quotes,
		Total:      int64(total),
		Page:       page,
		PageSize:   pageSize,
		TotalPages: int32(totalPages),
	}

	logger.Info("查詢報價列表成功", zap.Int("total", total))
	return c.JSON(http.StatusOK, response)
}

func (h *handler) CreateQuote(c echo.Context) error {
	logger := h.logger.Named("CreateQuote").With(zap.String("ip", c.RealIP()))
	logger.Info("創建新報價請求開始處理")

	// 解析請求資料
	var req CreateQuoteRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 從上下文中獲取用戶ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 根據用戶角色設定報價類型
	quoteType := sqlc.QuoteTypeValue0 // 預設為廠商報價
	if userRole == sqlc.UserRoleCISA {
		quoteType = sqlc.QuoteTypeValue1 // 軟協報價
	} else if userRole == sqlc.UserRoleSPO {
		quoteType = sqlc.QuoteTypeValue2 // 辦公室報價
	}

	// 創建報價模型
	quote := &models.Quote{
		ProductID:      req.ProductID,
		UserID:         userID,
		QuoteType:      quoteType,
		MarketPrice:    req.MarketPrice,
		InternetPrice:  req.InternetPrice,
		OriginalPrice:  req.OriginalPrice,
		PromotionPrice: req.PromotionPrice,
		BidPrice:       req.BidPrice,
		SameAsBidPrice: req.SameAsBidPrice,
		Remark:         req.Remark,
	}

	// 處理檔案上傳
	var attachments []*multipart.FileHeader
	form, err := c.MultipartForm()
	if err == nil && form.File != nil {
		if files, ok := form.File["files"]; ok && len(files) > 0 {
			attachments = files
		}
	}

	// 創建報價
	createdQuote, err := h.quote.CreateQuote(c.Request().Context(), userID, quote, attachments)
	if err != nil {
		if errors.Is(err, quotedomain.ErrProductNotFound) {
			logger.Error("產品不存在", zap.Uint32("productID", req.ProductID))
			return echo.NewHTTPError(http.StatusNotFound, "產品不存在")
		}
		if errors.Is(err, quotedomain.ErrProjectNotFound) {
			logger.Error("專案不存在", zap.Uint32("projectID", req.ProjectID))
			return echo.NewHTTPError(http.StatusNotFound, "專案不存在")
		}
		if errors.Is(err, quotedomain.ErrInvalidFillingTime) {
			logger.Error("不在允許填寫的時間範圍內")
			return echo.NewHTTPError(http.StatusForbidden, "不在允許填寫的時間範圍內")
		}
		if errors.Is(err, quotedomain.ErrInvalidQuote) {
			logger.Error("報價資料無效")
			return echo.NewHTTPError(http.StatusBadRequest, "報價資料無效，至少需要填寫一個非促銷價格")
		}
		logger.Error("創建報價失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "創建報價失敗")
	}

	logger.Info("創建報價成功", zap.Uint32("quoteID", createdQuote.ID))
	return c.JSON(http.StatusCreated, Response{Quote: createdQuote})
}

func (h *handler) UpdateQuote(c echo.Context) error {
	logger := h.logger.Named("UpdateQuote").With(zap.String("ip", c.RealIP()))
	logger.Info("更新報價請求開始處理")

	// 從路徑參數中獲取報價ID
	quoteIDStr := c.Param("id")
	if quoteIDStr == "" {
		logger.Error("報價ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "報價ID不可為空")
	}

	quoteID, err := strconv.ParseUint(quoteIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的報價ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的報價ID")
	}

	// 解析請求資料
	var req UpdateQuoteRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 從上下文中獲取用戶ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 先獲取現有報價資料
	existingQuote, err := h.quote.GetQuote(c.Request().Context(), uint32(quoteID))
	if err != nil {
		if errors.Is(err, quotedomain.ErrQuoteNotFound) {
			logger.Error("報價不存在", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價不存在")
		}
		if errors.Is(err, quotedomain.ErrQuoteAlreadyDeleted) {
			logger.Error("報價已刪除", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價已刪除")
		}
		logger.Error("獲取報價資料失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "獲取報價資料失敗")
	}

	// 更新報價模型
	updatedQuote := &models.Quote{
		ID:             uint32(quoteID),
		ProjectID:      existingQuote.Quote.ProjectID,
		ProductID:      existingQuote.Quote.ProductID,
		UserID:         existingQuote.Quote.UserID,
		QuoteType:      existingQuote.Quote.QuoteType,
		MarketPrice:    req.MarketPrice,
		InternetPrice:  req.InternetPrice,
		OriginalPrice:  req.OriginalPrice,
		PromotionPrice: req.PromotionPrice,
		BidPrice:       req.BidPrice,
		SameAsBidPrice: req.SameAsBidPrice,
		Remark:         req.Remark,
	}

	// 處理檔案上傳
	var attachments []*multipart.FileHeader
	form, err := c.MultipartForm()
	if err == nil && form.File != nil {
		if files, ok := form.File["files"]; ok && len(files) > 0 {
			attachments = files
		}
	}

	// 更新報價
	err = h.quote.UpdateQuote(c.Request().Context(), userID, userRole, updatedQuote, attachments)
	if err != nil {
		if errors.Is(err, quotedomain.ErrQuoteNotFound) {
			logger.Error("報價不存在", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價不存在")
		}
		if errors.Is(err, quotedomain.ErrQuoteAlreadyDeleted) {
			logger.Error("報價已刪除", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價已刪除")
		}
		if errors.Is(err, quotedomain.ErrQuoteAlreadyApproved) {
			logger.Error("報價已通過審核，無法修改")
			return echo.NewHTTPError(http.StatusForbidden, "報價已通過審核，無法修改")
		}
		if errors.Is(err, quotedomain.ErrUnauthorized) {
			logger.Error("無權限執行此操作")
			return echo.NewHTTPError(http.StatusForbidden, "無權限執行此操作")
		}
		if errors.Is(err, quotedomain.ErrInvalidFillingTime) {
			logger.Error("不在允許填寫的時間範圍內")
			return echo.NewHTTPError(http.StatusForbidden, "不在允許填寫的時間範圍內")
		}
		if errors.Is(err, quotedomain.ErrInvalidQuote) {
			logger.Error("報價資料無效")
			return echo.NewHTTPError(http.StatusBadRequest, "報價資料無效，至少需要填寫一個非促銷價格")
		}
		logger.Error("更新報價失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "更新報價失敗")
	}

	// 更新成功後，重新獲取報價詳情
	updatedQuoteDetail, err := h.quote.GetQuote(c.Request().Context(), uint32(quoteID))
	if err != nil {
		logger.Error("獲取更新後的報價詳情失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "更新報價成功，但獲取詳情失敗")
	}

	logger.Info("更新報價成功", zap.Uint64("quoteID", quoteID))
	return c.JSON(http.StatusOK, DetailResponse{QuoteDetail: updatedQuoteDetail})
}

func (h *handler) DeleteQuote(c echo.Context) error {
	logger := h.logger.Named("DeleteQuote").With(zap.String("ip", c.RealIP()))
	logger.Info("刪除報價請求開始處理")

	// 從路徑參數中獲取報價ID
	quoteIDStr := c.Param("id")
	if quoteIDStr == "" {
		logger.Error("報價ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "報價ID不可為空")
	}

	quoteID, err := strconv.ParseUint(quoteIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的報價ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的報價ID")
	}

	// 從上下文中獲取用戶ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 刪除報價
	err = h.quote.DeleteQuote(c.Request().Context(), uint32(quoteID), userID, userRole)
	if err != nil {
		if errors.Is(err, quotedomain.ErrQuoteNotFound) {
			logger.Error("報價不存在", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價不存在")
		}
		if errors.Is(err, quotedomain.ErrQuoteAlreadyDeleted) {
			logger.Error("報價已刪除", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價已刪除")
		}
		if errors.Is(err, quotedomain.ErrQuoteAlreadyApproved) {
			logger.Error("報價已通過審核，無法刪除")
			return echo.NewHTTPError(http.StatusForbidden, "報價已通過審核，無法刪除")
		}
		if errors.Is(err, quotedomain.ErrUnauthorized) {
			logger.Error("無權限執行此操作")
			return echo.NewHTTPError(http.StatusForbidden, "無權限執行此操作")
		}
		if errors.Is(err, quotedomain.ErrInvalidFillingTime) {
			logger.Error("不在允許填寫的時間範圍內")
			return echo.NewHTTPError(http.StatusForbidden, "不在允許填寫的時間範圍內")
		}
		logger.Error("刪除報價失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "刪除報價失敗")
	}

	logger.Info("刪除報價成功", zap.Uint64("quoteID", quoteID))
	return c.NoContent(http.StatusOK)
}

func (h *handler) ApproveQuote(c echo.Context) error {
	logger := h.logger.Named("ApproveQuote").With(zap.String("ip", c.RealIP()))
	logger.Info("審核通過報價請求開始處理")

	// 從路徑參數中獲取報價ID
	quoteIDStr := c.Param("id")
	if quoteIDStr == "" {
		logger.Error("報價ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "報價ID不可為空")
	}

	quoteID, err := strconv.ParseUint(quoteIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的報價ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的報價ID")
	}

	// 從上下文中獲取用戶ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 解析請求資料
	var req ReviewQuoteRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 確保狀態為「通過」
	if req.Status != sqlc.QuoteStatusValue1 {
		logger.Error("狀態必須為通過", zap.String("status", string(req.Status)))
		return echo.NewHTTPError(http.StatusBadRequest, "狀態必須為通過")
	}

	// 審核通過報價
	err = h.quote.ReviewQuote(c.Request().Context(), uint32(quoteID), userID, userRole, sqlc.QuoteStatusValue1, req.AdminRemark)
	if err != nil {
		if errors.Is(err, quotedomain.ErrQuoteNotFound) {
			logger.Error("報價不存在", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價不存在")
		}
		if errors.Is(err, quotedomain.ErrQuoteAlreadyDeleted) {
			logger.Error("報價已刪除", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價已刪除")
		}
		if errors.Is(err, quotedomain.ErrUnauthorized) {
			logger.Error("無權限執行此操作")
			return echo.NewHTTPError(http.StatusForbidden, "無權限執行此操作")
		}
		if errors.Is(err, quotedomain.ErrInvalidQuoteStatus) {
			logger.Error("報價狀態無效，無法審核")
			return echo.NewHTTPError(http.StatusBadRequest, "報價狀態無效，只能審核待審或重送狀態的報價")
		}
		logger.Error("審核報價失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "審核報價失敗")
	}

	// 審核成功後，重新獲取報價詳情
	updatedQuoteDetail, err := h.quote.GetQuote(c.Request().Context(), uint32(quoteID))
	if err != nil {
		logger.Error("獲取更新後的報價詳情失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "審核報價成功，但獲取詳情失敗")
	}

	logger.Info("審核通過報價成功", zap.Uint64("quoteID", quoteID))
	return c.JSON(http.StatusOK, DetailResponse{QuoteDetail: updatedQuoteDetail})
}

func (h *handler) RejectQuote(c echo.Context) error {
	logger := h.logger.Named("RejectQuote").With(zap.String("ip", c.RealIP()))
	logger.Info("審核退件報價請求開始處理")

	// 從路徑參數中獲取報價ID
	quoteIDStr := c.Param("id")
	if quoteIDStr == "" {
		logger.Error("報價ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "報價ID不可為空")
	}

	quoteID, err := strconv.ParseUint(quoteIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的報價ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的報價ID")
	}

	// 從上下文中獲取用戶ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 解析請求資料
	var req ReviewQuoteRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 確保狀態為「退件」
	if req.Status != sqlc.QuoteStatusValue2 {
		logger.Error("狀態必須為退件", zap.String("status", string(req.Status)))
		return echo.NewHTTPError(http.StatusBadRequest, "狀態必須為退件")
	}

	// 確保退件原因不為空
	if req.AdminRemark == "" {
		logger.Error("退件原因不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "退件原因不可為空")
	}

	// 審核退件報價
	err = h.quote.ReviewQuote(c.Request().Context(), uint32(quoteID), userID, userRole, sqlc.QuoteStatusValue2, req.AdminRemark)
	if err != nil {
		if errors.Is(err, quotedomain.ErrQuoteNotFound) {
			logger.Error("報價不存在", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價不存在")
		}
		if errors.Is(err, quotedomain.ErrQuoteAlreadyDeleted) {
			logger.Error("報價已刪除", zap.Uint64("quoteID", quoteID))
			return echo.NewHTTPError(http.StatusNotFound, "報價已刪除")
		}
		if errors.Is(err, quotedomain.ErrUnauthorized) {
			logger.Error("無權限執行此操作")
			return echo.NewHTTPError(http.StatusForbidden, "無權限執行此操作")
		}
		if errors.Is(err, quotedomain.ErrInvalidQuoteStatus) {
			logger.Error("報價狀態無效，無法審核")
			return echo.NewHTTPError(http.StatusBadRequest, "報價狀態無效，只能審核待審或重送狀態的報價")
		}
		logger.Error("審核報價失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "審核報價失敗")
	}

	// 審核成功後，重新獲取報價詳情
	updatedQuoteDetail, err := h.quote.GetQuote(c.Request().Context(), uint32(quoteID))
	if err != nil {
		logger.Error("獲取更新後的報價詳情失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "審核報價成功，但獲取詳情失敗")
	}

	logger.Info("審核退件報價成功", zap.Uint64("quoteID", quoteID))
	return c.JSON(http.StatusOK, DetailResponse{QuoteDetail: updatedQuoteDetail})
}

func (h *handler) BatchApproveQuotes(c echo.Context) error {
	logger := h.logger.Named("BatchApproveQuotes").With(zap.String("ip", c.RealIP()))
	logger.Info("批量審核通過報價請求開始處理")

	// 從上下文中獲取用戶ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 解析請求資料
	var req BatchReviewQuoteRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 確保狀態為「通過」
	if req.Status != sqlc.QuoteStatusValue1 {
		logger.Error("狀態必須為通過", zap.String("status", string(req.Status)))
		return echo.NewHTTPError(http.StatusBadRequest, "狀態必須為通過")
	}

	// 批量審核通過報價
	if err := h.quote.BatchReviewQuotes(c.Request().Context(), userID, userRole, req.QuoteIDs, sqlc.QuoteStatusValue1, req.AdminRemark); err != nil {
		if errors.Is(err, quotedomain.ErrUnauthorized) {
			logger.Error("無權限執行此操作")
			return echo.NewHTTPError(http.StatusForbidden, "無權限執行此操作")
		}
		logger.Error("批量審核報價失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "批量審核報價失敗")
	}

	return c.NoContent(http.StatusOK)
}

func (h *handler) BatchRejectQuotes(c echo.Context) error {
	logger := h.logger.Named("BatchRejectQuotes").With(zap.String("ip", c.RealIP()))
	logger.Info("批量審核退件報價請求開始處理")

	// 從上下文中獲取用戶ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 解析請求資料
	var req BatchReviewQuoteRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 確保狀態為「退件」
	if req.Status != sqlc.QuoteStatusValue2 {
		logger.Error("狀態必須為退件", zap.String("status", string(req.Status)))
		return echo.NewHTTPError(http.StatusBadRequest, "狀態必須為退件")
	}

	// 確保退件原因不為空
	if req.AdminRemark == "" {
		logger.Error("退件原因不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "退件原因不可為空")
	}

	// 批量審核退件報價
	if err := h.quote.BatchReviewQuotes(c.Request().Context(), userID, userRole, req.QuoteIDs, sqlc.QuoteStatusValue2, req.AdminRemark); err != nil {
		if errors.Is(err, quotedomain.ErrUnauthorized) {
			logger.Error("無權限執行此操作")
			return echo.NewHTTPError(http.StatusForbidden, "無權限執行此操作")
		}
		logger.Error("批量審核報價失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "批量審核報價失敗")
	}

	return c.NoContent(http.StatusOK)
}

func (h *handler) GetQuoteStats(c echo.Context) error {
	logger := h.logger.Named("GetQuoteStats").With(zap.String("ip", c.RealIP()))
	logger.Info("獲取報價統計資訊請求開始處理")

	// 從查詢參數中獲取專案ID
	projectIDStr := c.QueryParam("project_id")
	if projectIDStr == "" {
		logger.Error("專案ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "專案ID不可為空")
	}

	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的專案ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的專案ID")
	}

	// TODO Get Quote Stats not Quote
	// 獲取報價統計資訊
	stats, err := h.quote.GetQuote(c.Request().Context(), uint32(projectID))
	if err != nil {
		if errors.Is(err, quotedomain.ErrProjectNotFound) {
			logger.Error("專案不存在", zap.Uint64("projectID", projectID))
			return echo.NewHTTPError(http.StatusNotFound, "專案不存在")
		}
		logger.Error("獲取報價統計資訊失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "獲取報價統計資訊失敗")
	}

	logger.Info("獲取報價統計資訊成功", zap.Uint64("projectID", projectID))
	return c.JSON(http.StatusOK, stats)
}

// ListQuotesV2 處理報價列表查詢請求（新版本，使用POST請求體）
func (h *handler) ListQuotesV2(c echo.Context) error {
	logger := h.logger.Named("ListQuotesV2").With(zap.String("ip", c.RealIP()))
	logger.Info("報價列表查詢請求開始處理")

	// 解析請求資料
	var req QuoteListRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: "無效的請求資料: " + err.Error()})
	}

	// 驗證請求資料
	if err := h.validator.Struct(req); err != nil {
		logger.Error("請求資料驗證失敗", zap.Error(err))
		return c.JSON(http.StatusBadRequest, APIErrorResponse{Message: "無效的請求參數: " + err.Error()})
	}

	// 設定預設分頁參數
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100 // 限制最大每頁數量
	}

	// 設定預設排序
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortDir == "" {
		req.SortDir = "desc"
	}

	// 從上下文中獲取使用者ID和角色
	var userID uint32
	var userRole sqlc.UserRole

	if userIDValue := c.Get(models.UserID); userIDValue != nil {
		userID = userIDValue.(uint32)
	} else {
		logger.Error("無法獲取使用者ID")
		return c.JSON(http.StatusUnauthorized, APIErrorResponse{Message: "請先登入"})
	}

	if userRoleValue := c.Get(models.UserRole); userRoleValue != nil {
		userRole = userRoleValue.(sqlc.UserRole)
	} else {
		logger.Error("無法獲取使用者角色")
		return c.JSON(http.StatusUnauthorized, APIErrorResponse{Message: "請先登入"})
	}

	// 計算分頁參數
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	// 構建查詢參數
	params := sqlc.ListQuotesParams{
		ProjectID:  int64(0), // 預設為0，表示不篩選專案
		ProductID:  int64(0), // 預設為0，表示不篩選產品
		UserID:     int64(0), // 預設為0，表示不篩選用戶
		CompanyID:  int64(0), // 預設為0，表示不篩選廠商
		QuoteType:  req.QuoteType,
		Status:     req.Status,
		GroupID:    int64(0), // 預設為0，表示不篩選產品組別
		SearchTerm: req.SearchTerm,
		SortBy:     req.SortBy,
		SortDir:    req.SortDir,
		OffsetVal:  offset,
		LimitVal:   limit,
	}

	// 設定篩選條件
	if req.ProjectID != nil && *req.ProjectID > 0 {
		params.ProjectID = int64(*req.ProjectID)
	}
	if req.ProductID != nil && *req.ProductID > 0 {
		params.ProductID = int64(*req.ProductID)
	}
	if req.UserID != nil && *req.UserID > 0 {
		params.UserID = int64(*req.UserID)
	}

	// 根據使用者角色設定權限控制
	if userRole == sqlc.UserRoleCompany {
		// 廠商用戶只能查看自己的報價
		params.UserID = int64(userID)

		// 如果指定了專案ID，需要檢查權限
		if req.ProjectID != nil && *req.ProjectID > 0 {
			// 檢查使用者是否有權限訪問該專案
			canAccess, err := h.project.CanAccessProject(c.Request().Context(), userID, *req.ProjectID)
			if err != nil {
				logger.Error("檢查專案訪問權限失敗", zap.Error(err))
				return c.JSON(http.StatusInternalServerError, APIErrorResponse{Message: "檢查專案訪問權限失敗"})
			}
			if !canAccess {
				logger.Error("無權限訪問專案", zap.Uint32("userID", userID), zap.Uint32("projectID", *req.ProjectID))
				return c.JSON(http.StatusForbidden, APIErrorResponse{Message: "無權限訪問專案"})
			}
		}
	} else {
		// SPO和CISA可以查看所有報價，但如果指定了專案ID，需要檢查權限
		if req.ProjectID != nil && *req.ProjectID > 0 {
			// 檢查使用者是否有權限訪問該專案
			canAccess, err := h.project.CanAccessProject(c.Request().Context(), userID, *req.ProjectID)
			if err != nil {
				logger.Error("檢查專案訪問權限失敗", zap.Error(err))
				return c.JSON(http.StatusInternalServerError, APIErrorResponse{Message: "檢查專案訪問權限失敗"})
			}
			if !canAccess {
				logger.Error("無權限訪問專案", zap.Uint32("userID", userID), zap.Uint32("projectID", *req.ProjectID))
				return c.JSON(http.StatusForbidden, APIErrorResponse{Message: "無權限訪問專案"})
			}
		}
	}

	// 查詢報價列表
	quotes, total, err := h.quote.ListQuotes(c.Request().Context(), params)
	if err != nil {
		logger.Error("獲取報價列表失敗", zap.Error(err))
		return c.JSON(http.StatusInternalServerError, APIErrorResponse{Message: "獲取報價列表失敗"})
	}

	// 構建回應
	response := QuoteListResponse{
		Quotes: quotes,
		Pagination: PaginationInfo{
			Page:      req.Page,
			PageSize:  req.PageSize,
			Total:     total,
			TotalPage: (total + int64(req.PageSize) - 1) / int64(req.PageSize),
		},
		Filters: req,
	}

	logger.Info("報價列表查詢成功",
		zap.Int32("page", req.Page),
		zap.Int32("pageSize", req.PageSize),
		zap.Int64("total", total),
		zap.Int("count", len(quotes)))

	return c.JSON(http.StatusOK, response)
}
