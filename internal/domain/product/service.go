package productdomain

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/jackc/pgx/v5"

	"pms-api/internal/domain/file"
	"pms-api/internal/domain/product/group"
	"pms-api/internal/domain/project"
	"pms-api/internal/domain/project/log"
	"pms-api/internal/domain/user"
	"pms-api/internal/driver"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var (
	// ErrProductNotFound 表示找不到指定的產品記錄
	ErrProductNotFound = errors.New("產品不存在")

	// ErrProductGroupNotFound 表示找不到指定的產品組別記錄
	ErrProductGroupNotFound = errors.New("產品組別不存在")

	// ErrProjectNotFound 表示找不到指定的專案
	ErrProjectNotFound = errors.New("專案不存在")

	// ErrInvalidParameter 表示提供的參數無效
	// 例如，傳入的名稱為空、項次格式不正確等
	ErrInvalidParameter = errors.New("無效的參數")

	// ErrUnauthorized 表示用戶沒有權限執行請求的操作
	// 例如，非管理員用戶嘗試匯入產品或刪除其他用戶建立的產品
	ErrUnauthorized = errors.New("無權限執行此操作")

	// ErrProductAlreadyDeleted 表示產品已被邏輯刪除
	ErrProductAlreadyDeleted = errors.New("產品已被刪除")

	// ErrProductNotDeleted 表示產品未被刪除
	ErrProductNotDeleted = errors.New("產品未被刪除")

	// ErrImportFailed 表示產品匯入過程中發生錯誤
	ErrImportFailed = errors.New("匯入產品失敗")

	// ErrDeleteFailed 表示產品刪除過程中發生錯誤
	ErrDeleteFailed = errors.New("刪除產品失敗")

	// ErrDuplicateItemNo 表示在同一組別中已存在相同項次的產品
	ErrDuplicateItemNo = errors.New("項次已存在")

	// ErrDuplicateGroupCode 表示在同一專案中已存在相同編號的組別
	ErrDuplicateGroupCode = errors.New("組別編號已存在")
)

// Service 定義產品領域的業務邏輯服務接口
// 提供產品管理相關的所有操作方法
type Service interface {
	// GetByID 根據ID獲取產品詳情
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 產品的唯一識別碼
	//
	// 返回:
	// - *models.Product: 找到的產品詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByID(ctx context.Context, id uint32) (*models.Product, error)

	// GetByGroupAndItemID 根據組別ID和項次獲取產品詳情
	//
	// 參數:
	// - ctx: 操作上下文
	// - groupID: 產品組別ID
	// - itemID: 產品項次
	//
	// 返回:
	// - *models.Product: 找到的產品詳情
	// - error: 可能的錯誤
	GetByGroupAndItemID(ctx context.Context, groupID uint32, itemID int32) (*models.Product, error)

	// ListByGroupID 根據組別ID獲取產品列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - groupID: 產品組別ID
	// - includeDeleted: 是否包含已刪除的產品
	//
	// 返回:
	// - []*models.Product: 產品列表
	// - error: 可能的錯誤
	ListByGroupID(ctx context.Context, groupID uint32, includeDeleted bool) ([]*models.Product, error)

	// ListByProjectID 根據專案ID獲取產品列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	// - params: 查詢參數，包含類別、品牌、搜尋詞等過濾條件
	//
	// 返回:
	// - []*models.Product: 產品列表
	// - error: 可能的錯誤
	ListByProjectID(ctx context.Context, projectID uint32, params sqlc.ListProductsByProjectIDParams) ([]*models.Product, error)

	// CreateMany 批量創建產品
	//
	// 參數:
	// - ctx: 操作上下文
	// - products: 產品資料列表
	//
	// 返回:
	// - error: 可能的錯誤
	//
	// 用途:
	// 用於在匯入產品時批量創建新產品，或在其他需要一次性創建多個產品的情況下使用
	// 注意應確保所提供產品資料的有效性，包括必填欄位、唯一性約束等
	// 通常搭配事務使用以確保資料一致性
	CreateMany(ctx context.Context, products []*models.Product, projectID, userID uint32) error
}

// service 實現了 Service 接口的結構體
// 依賴多個領域Repository來完成產品相關的業務邏輯
type service struct {
	db           *driver.DB
	product      Repository                    // 產品資料存取層
	productGroup productgroupdomain.Repository // 產品組別資料存取層
	project      projectdomain.Repository      // 專案資料存取層
	projectLog   projectlogdomain.Repository   // 專案日誌資料存取層
	file         filedomain.Service            // 檔案服務，用於處理上傳/下載等操作
	userRepo     userdomain.Repository         // 用戶資料存取層
	logger       *zap.Logger                   // 日誌記錄器
}

// NewService 創建 Service 實例
//
// 參數:
// - db: 資料庫連接
// - productRepo: 產品資料存取層
// - productGroupRepo: 產品組別資料存取層
// - projectRepo: 專案資料存取層
// - projectLogRepo: 專案日誌資料存取層
// - fileService: 檔案服務
// - userRepo: 用戶資料存取層
// - logger: 日誌記錄器
//
// 返回:
// - Service: 產品服務實例
func NewService(
	db *driver.DB,
	productRepo Repository,
	projectRepo projectdomain.Repository,
	projectLogRepo projectlogdomain.Repository,
	fileService filedomain.Service,
	userRepo userdomain.Repository,
	logger *zap.Logger,
) Service {
	return &service{
		db:           db,
		product:      productRepo,
		productGroup: productGroupRepo,
		project:      projectRepo,
		projectLog:   projectLogRepo,
		file:         fileService,
		userRepo:     userRepo,
		logger:       logger.Named("Service").Named("Product"),
	}
}

// GetByID 實現了 Service 接口的 GetByID 方法
// 根據ID獲取產品詳情
func (s *service) GetByID(ctx context.Context, id uint32) (*models.Product, error) {
	logger := s.logger.Named("GetByID")

	// 參數校驗
	if id == 0 {
		logger.Error("獲取產品失敗", zap.Error(ErrInvalidParameter))
		return nil, ErrInvalidParameter
	}

	// 調用 Repository 層獲取產品
	product, err := s.product.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取產品失敗", zap.Error(err), zap.Uint32("id", id))
		return nil, fmt.Errorf("獲取產品失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("獲取產品成功", zap.Uint32("id", id))

	return product, nil
}

// GetByGroupAndItemID 實現了 Service 接口的 GetByGroupAndItemID 方法
// 根據組別ID和項次獲取產品詳情
func (s *service) GetByGroupAndItemID(ctx context.Context, groupID uint32, itemID int32) (*models.Product, error) {
	logger := s.logger.Named("GetByGroupAndItemID")

	// 參數校驗
	if groupID == 0 {
		logger.Error("獲取產品失敗", zap.Error(ErrInvalidParameter), zap.String("reason", "組別ID不可為空"))
		return nil, ErrInvalidParameter
	}

	// 調用 Repository 層獲取產品
	product, err := s.product.GetByGroupAndItemID(ctx, groupID, itemID)
	if err != nil {
		logger.Error("獲取產品失敗",
			zap.Error(err),
			zap.Uint32("groupID", groupID),
			zap.Int32("itemID", itemID))
		return nil, fmt.Errorf("獲取產品失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("獲取產品成功",
		zap.Uint32("groupID", groupID),
		zap.Int32("itemID", itemID))

	return product, nil
}

// ListByGroupID 實現了 Service 接口的 ListByGroupID 方法
// 根據組別ID獲取產品列表
func (s *service) ListByGroupID(ctx context.Context, groupID uint32, includeDeleted bool) ([]*models.Product, error) {
	logger := s.logger.Named("ListByGroupID")

	// 參數校驗
	if groupID == 0 {
		logger.Error("獲取產品列表失敗", zap.Error(ErrInvalidParameter), zap.String("reason", "組別ID不可為空"))
		return nil, ErrInvalidParameter
	}

	// 檢查組別是否存在
	group, err := s.productGroup.GetByID(ctx, groupID)
	if err != nil {
		logger.Error("獲取產品組別失敗", zap.Error(err), zap.Uint32("groupID", groupID))
		return nil, ErrProductGroupNotFound
	}

	// 調用 Repository 層獲取產品列表
	products, err := s.product.ListByGroupID(ctx, groupID, includeDeleted)
	if err != nil {
		logger.Error("獲取產品列表失敗",
			zap.Error(err),
			zap.Uint32("groupID", groupID),
			zap.Bool("includeDeleted", includeDeleted))
		return nil, fmt.Errorf("獲取產品列表失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("獲取產品列表成功",
		zap.Uint32("groupID", groupID),
		zap.String("groupName", group.Name),
		zap.Bool("includeDeleted", includeDeleted),
		zap.Int("count", len(products)))

	return products, nil
}

// ListByProjectID 實現了 Service 接口的 ListByProjectID 方法
// 根據專案ID獲取產品列表
func (s *service) ListByProjectID(ctx context.Context, projectID uint32, params sqlc.ListProductsByProjectIDParams) ([]*models.Product, error) {
	logger := s.logger.Named("ListByProjectID")

	// 參數校驗
	if projectID == 0 {
		logger.Error("獲取產品列表失敗", zap.Error(ErrInvalidParameter), zap.String("reason", "專案ID不可為空"))
		return nil, ErrInvalidParameter
	}

	// 檢查專案是否存在
	project, err := s.project.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案失敗", zap.Error(err), zap.Uint32("projectID", projectID))
		return nil, ErrProjectNotFound
	}

	// 調用 Repository 層獲取產品列表
	products, err := s.product.ListByProjectID(ctx, projectID, params)
	if err != nil {
		logger.Error("獲取產品列表失敗",
			zap.Error(err),
			zap.Uint32("projectID", projectID))
		return nil, fmt.Errorf("獲取產品列表失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("獲取產品列表成功",
		zap.Uint32("projectID", projectID),
		zap.String("projectName", project.Name),
		zap.Int("count", len(products)))

	return products, nil
}

func (s *service) CreateMany(ctx context.Context, products []*models.Product, projectID, userId uint32) error {
	logger := s.logger.Named("CreateMany")

	// 參數校驗
	if len(products) == 0 {
		logger.Error("批量新增產品失敗", zap.Error(ErrInvalidParameter), zap.String("reason", "產品列表不可為空"))
		return ErrInvalidParameter
	}

	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{
		IsoLevel:       pgx.ReadCommitted, // 足夠的隔離級別
		AccessMode:     pgx.ReadWrite,
		DeferrableMode: pgx.NotDeferrable,
	})
	if err != nil {
		return fmt.Errorf("開始事務失敗: %w", err)
	}
	defer func(tx pgx.Tx, ctx context.Context) {
		if err != nil {
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("回滾事務失敗", zap.Error(rbErr))
			}
		}
	}(tx, ctx) // 確保出錯時自動回滾

	optimizationQueries := []string{
		"SET LOCAL synchronous_commit = OFF",
		"SET LOCAL work_mem = '64MB'",
		"SET LOCAL maintenance_work_mem = '1GB'",
		"SET LOCAL temp_buffers = '64MB'",
	}

	for _, query := range optimizationQueries {
		if _, err := tx.Exec(ctx, query); err != nil {
			s.logger.Warn("優化設定失敗", zap.String("query", query))
		}
	}

	pids := make([]int32, 0, len(products))
	productVats := make([]string, 0, len(products))
	groupIds := make([]int32, 0, len(products))
	itemIds := make([]int32, 0, len(products))
	names := make([]string, 0, len(products))

	batchInsertAllProductsParam := make([]sqlc.BatchInsertAllProductsParams, 0, len(products))
	projectLogs := make([]*models.ProjectLog, 0, len(products))

	for i := range products {
		pids = append(pids, products[i].PID)
		productVats = append(productVats, products[i].ProductVAT)
		groupIds = append(groupIds, int32(products[i].GroupID))
		itemIds = append(itemIds, products[i].ItemID)
		names = append(names, products[i].Name)
		batchInsertAllProductsParam = append(batchInsertAllProductsParam, sqlc.BatchInsertAllProductsParams{
			Pid:            products[i].PID,
			ProductVat:     products[i].ProductVAT,
			ProductCompany: products[i].ProductCompany,
			GroupID:        products[i].GroupID,
			GroupName:      products[i].GroupName,
			ItemID:         products[i].ItemID,
			Brand:          &products[i].Brand,
			Name:           products[i].Name,
			ProductNation:  &products[i].ProductNation,
			UnitType:       &products[i].UnitType,
			Category:       &products[i].Category,
			Info:           &products[i].Info,
			Auth:           products[i].Auth,
			Price:          products[i].Price,
			PriceInvoice:   products[i].PriceInvoice,
			BidPrice:       &products[i].BidPrice,
			AuthPc:         int16(products[i].AuthPC),
			AuthSvr:        int16(products[i].AuthSVR),
			AuthCal:        int16(products[i].AuthCAL),
			AuthMobile:     int16(products[i].AuthMobile),
			AuthCore:       int16(products[i].AuthCore),
			ShipAuth:       int16(products[i].ShipAuth),
			ShipBox:        int16(products[i].ShipBox),
			ShipDisk:       int16(products[i].ShipDisk),
			Memo:           &products[i].Memo,
			StepStart:      products[i].StepStart,
			StepEnd:        products[i].StepEnd,
			ProjectID:      projectID,
			CreatedBy:      userId,
			UpdatedBy:      userId,
		})

		projectLogs = append(projectLogs, &models.ProjectLog{
			UserID:       &userId,
			ProductID:    &products[i].ID,
			LogType:      sqlc.ProjectLogTypeValue0, // 品項建立
			Message:      fmt.Sprintf("用戶 %d 建立了品項 %d", userId, products[i].PID),
			CreatedAt:    time.Now(),
			PID:          products[i].PID,
			ProductGroup: products[i].GroupName,
		})
	}

	logger.Info("批量創建產品開始", zap.Int("count", len(products)))

	// 找出重複的產品
	duplicates, err := s.product.FindAndHardDeleteDuplicatesForImport(ctx, sqlc.FindAndHardDeleteDuplicatesForImportParams{
		Pids:        pids,
		ProductVats: productVats,
		GroupIds:    groupIds,
		ItemIds:     itemIds,
		Names:       names,
	}, tx)
	if err != nil {
		logger.Error("標記重複產品失敗", zap.Error(err))
		return err
	}

	// 軟刪除重複的產品
	if len(duplicates) > 0 {
		logger.Info("標記重複產品成功", zap.Int("count", len(duplicates)))
		var projectDeleteLogs []*models.ProjectLog
		for _, duplicate := range duplicates {
			projectDeleteLogs = append(projectDeleteLogs, &models.ProjectLog{
				UserID:    &userId,
				ProductID: &duplicate.ID,
				LogType:   sqlc.ProjectLogTypeValue2, // 品項刪除
				Message:   fmt.Sprintf("用戶 %d 刪除了重複的品項 %d", userId, duplicate.Pid),
				CreatedAt: time.Now(),
				PID:       duplicate.Pid,
			})
		}

		if err = s.projectLog.CreateManyWithTx(ctx, projectDeleteLogs, tx); err != nil {
			logger.Error("批量創建刪除日誌失敗", zap.Error(err))
			return err
		}
	}

	// 批量插入產品
	if err = s.product.BatchInsertAllProducts(ctx, batchInsertAllProductsParam, tx); err != nil {
		logger.Error("批量插入產品失敗", zap.Error(err))
		return err
	}

	// 批量創建操作日誌
	if err = s.projectLog.CreateManyWithTx(ctx, projectLogs, tx); err != nil {
		logger.Error("批量創建操作日誌失敗", zap.Error(err))
		return err
	}

	// 提交事務
	if err = tx.Commit(ctx); err != nil {
		logger.Error("提交事務失敗", zap.Error(err))
		return err
	}

	logger.Info("批量創建產品成功",
		zap.Int("inserted", len(products)),
		zap.Int("deleted", len(duplicates)))

	return nil
}
