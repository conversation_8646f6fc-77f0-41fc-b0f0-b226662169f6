package productdomain

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// 編譯時期檢查確保 repository 實現了 Repository 介面
var _ Repository = (*repository)(nil)

// Repository 定義產品數據存取的介面
// 提供對產品資料進行增刪改查的抽象方法
type Repository interface {

	// GetByID 根據ID獲取產品
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 產品的唯一識別碼
	//
	// 返回:
	// - *models.Product: 找到的產品詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByID(ctx context.Context, id uint32) (*models.Product, error)

	// ListByGroupID 根據組別ID獲取產品列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - groupID: 產品組別ID
	// - includeDeleted: 是否包含已刪除的產品
	//
	// 返回:
	// - []*models.Product: 產品列表
	// - error: 可能的錯誤
	ListByGroupID(ctx context.Context, groupID uint32, includeDeleted bool) ([]*models.Product, error)

	// ListByProjectID 根據專案ID獲取產品列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	// - params: 查詢參數，包含類別、品牌、搜尋詞等過濾條件
	//
	// 返回:
	// - []*models.Product: 產品列表
	// - error: 可能的錯誤
	ListByProjectID(ctx context.Context, projectID uint32, params sqlc.ListProductsByProjectIDParams) ([]*models.Product, error)

	// MarkDuplicatesForDeletion 標記重複的產品為待刪除
	//
	// 參數:
	// - ctx: 操作上下文
	// - params: 包含要檢查重複的產品資訊
	//
	// 返回:
	// - []*sqlc.MarkDuplicatesForDeletionRow: 被標記為重複的產品列表
	// - error: 可能的錯誤
	MarkDuplicatesForDeletion(ctx context.Context, params sqlc.MarkDuplicatesForDeletionParams, tx sqlc.DBTX) ([]*sqlc.MarkDuplicatesForDeletionRow, error)

	// DeleteDuplicates 刪除重複的產品
	//
	// 參數:
	// - ctx: 操作上下文
	// - ids: 要刪除的產品ID列表
	//
	// 返回:
	// - error: 可能的錯誤
	DeleteDuplicates(ctx context.Context, updatedBy uint32, ids []int64, tx sqlc.DBTX) error

	// BatchInsertAllProducts 批量插入產品
	//
	// 參數:
	// - ctx: 操作上下文
	// - params: 產品資料列表
	//
	// 返回:
	// - error: 可能的錯誤
	BatchInsertAllProducts(ctx context.Context, params []sqlc.BatchInsertAllProductsParams, tx sqlc.DBTX) error

	// FindAndHardDeleteDuplicatesForImport
	//
	// 參數:
	// - ctx: 操作上下文
	// - params: 包含要刪除的產品資訊
	//
	// 返回:
	// - []*sqlc.FindAndHardDeleteDuplicatesForImportRow: 被刪除的產品列表
	// - error: 可能的錯誤
	FindAndHardDeleteDuplicatesForImport(ctx context.Context, params sqlc.FindAndHardDeleteDuplicatesForImportParams, tx sqlc.DBTX) ([]*sqlc.FindAndHardDeleteDuplicatesForImportRow, error)
}

// repository 實現 Repository 介面的具體結構體
type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的產品資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 生成
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("Product"),
		querier: querier,
	}
}

// GetByID 實現了 Repository 介面的 GetByID 方法
// 根據ID獲取產品
func (r *repository) GetByID(ctx context.Context, id uint32) (*models.Product, error) {
	logger := r.logger.Named("GetByID")

	// 參數校驗
	if id == 0 {
		logger.Error("查詢產品失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("id 不可為空")
	}

	// 執行查詢
	sqlcProduct, err := r.querier.GetProductByID(ctx, id)
	if err != nil {
		logger.Error("查詢產品失敗", zap.Error(err), zap.Uint32("id", id))
		return nil, fmt.Errorf("查詢產品失敗: %w", err)
	}

	// 轉換查詢結果為領域模型
	product := convertToProduct(sqlcProduct)

	// 記錄成功日誌
	logger.Info("查詢產品成功", zap.Uint32("id", id))

	return product, nil
}

// ListByGroupID 實現了 Repository 介面的 ListByGroupID 方法
// 根據組別ID獲取產品列表
func (r *repository) ListByGroupID(ctx context.Context, groupID uint32, includeDeleted bool) ([]*models.Product, error) {
	logger := r.logger.Named("ListByGroupID")

	// 參數校驗
	if groupID == 0 {
		logger.Error("查詢產品列表失敗", zap.Error(errors.New("組別ID不可為空")))
		return nil, errors.New("組別ID不可為空")
	}

	// 執行查詢
	sqlcParams := sqlc.ListProductsByGroupIDParams{
		GroupID:        groupID,
		IncludeDeleted: includeDeleted,
	}

	sqlcProducts, err := r.querier.ListProductsByGroupID(ctx, sqlcParams)
	if err != nil {
		logger.Error("查詢產品列表失敗",
			zap.Error(err),
			zap.Uint32("groupID", groupID),
			zap.Bool("includeDeleted", includeDeleted))
		return nil, fmt.Errorf("查詢產品列表失敗: %w", err)
	}

	// 轉換查詢結果為領域模型列表
	products := make([]*models.Product, 0, len(sqlcProducts))
	for _, sqlcProduct := range sqlcProducts {
		products = append(products, convertToProduct(sqlcProduct))
	}

	// 記錄成功日誌
	logger.Info("查詢產品列表成功",
		zap.Uint32("groupID", groupID),
		zap.Bool("includeDeleted", includeDeleted),
		zap.Int("count", len(products)))

	return products, nil
}

// ListByProjectID 實現了 Repository 介面的 ListByProjectID 方法
// 根據專案ID獲取產品列表
func (r *repository) ListByProjectID(ctx context.Context, projectID uint32, params sqlc.ListProductsByProjectIDParams) ([]*models.Product, error) {
	logger := r.logger.Named("ListByProjectID")

	// 參數校驗
	if projectID == 0 {
		logger.Error("查詢產品列表失敗", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.New("專案ID不可為空")
	}

	// 設定專案ID
	params.ProjectID = projectID

	// 執行查詢
	sqlcProducts, err := r.querier.ListProductsByProjectID(ctx, params)
	if err != nil {
		logger.Error("查詢產品列表失敗",
			zap.Error(err),
			zap.Uint32("projectID", projectID))
		return nil, fmt.Errorf("查詢產品列表失敗: %w", err)
	}

	// 轉換查詢結果為領域模型列表
	products := make([]*models.Product, 0, len(sqlcProducts))
	for _, sqlcProduct := range sqlcProducts {
		products = append(products, convertToProduct(sqlcProduct))
	}

	// 記錄成功日誌
	logger.Info("查詢產品列表成功",
		zap.Uint32("projectID", projectID),
		zap.Int("count", len(products)))

	return products, nil
}

// MarkDuplicatesForDeletion 實現了 Repository 介面的 MarkDuplicatesForDeletion 方法
// 標記重複的產品為待刪除
func (r *repository) MarkDuplicatesForDeletion(ctx context.Context, params sqlc.MarkDuplicatesForDeletionParams, tx sqlc.DBTX) ([]*sqlc.MarkDuplicatesForDeletionRow, error) {
	logger := r.logger.Named("MarkDuplicatesForDeletion")

	// 參數校驗
	if len(params.Pids) == 0 || len(params.ProductVats) == 0 || len(params.GroupIds) == 0 || len(params.ItemIds) == 0 || len(params.Names) == 0 {
		logger.Error("標記重複產品失敗", zap.Error(errors.New("參數不完整")))
		return nil, errors.New("參數不完整")
	}

	querier := sqlc.New(tx)

	// 執行查詢
	duplicates, err := querier.MarkDuplicatesForDeletion(ctx, params)
	if err != nil {
		logger.Error("標記重複產品失敗", zap.Error(err))
		return nil, fmt.Errorf("標記重複產品失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("標記重複產品成功", zap.Int("count", len(duplicates)))

	return duplicates, nil
}

// DeleteDuplicates 實現了 Repository 介面的 DeleteDuplicates 方法
// 軟刪除重複的產品
func (r *repository) DeleteDuplicates(ctx context.Context, updatedBy uint32, ids []int64, tx sqlc.DBTX) error {
	logger := r.logger.Named("SoftDeleteDuplicates")

	// 參數校驗
	if len(ids) == 0 {
		logger.Error("刪除重複產品失敗", zap.Error(errors.New("產品ID列表不可為空")))
		return errors.New("產品ID列表不可為空")
	}

	querier := sqlc.New(tx)

	// 執行軟刪除
	if err := querier.DeleteDuplicates(ctx, ids); err != nil {
		logger.Error("刪除重複產品失敗", zap.Error(err), zap.Int("count", len(ids)))
		return fmt.Errorf("刪除重複產品失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("刪除重複產品成功", zap.Int("count", len(ids)))
	return nil
}

// BatchInsertAllProducts 實現了 Repository 介面的 BatchInsertAllProducts 方法
// 批量插入產品
func (r *repository) BatchInsertAllProducts(ctx context.Context, params []sqlc.BatchInsertAllProductsParams, tx sqlc.DBTX) error {
	logger := r.logger.Named("BatchInsertAllProducts")

	// 參數校驗
	if len(params) == 0 {
		logger.Error("批量插入產品失敗", zap.Error(errors.New("產品列表不可為空")))
		return errors.New("產品列表不可為空")
	}

	querier := sqlc.New(tx)

	// 執行批量插入
	if _, err := querier.BatchInsertAllProducts(ctx, params); err != nil {
		logger.Error("批量插入產品失敗", zap.Error(err), zap.Int("count", len(params)))
		return fmt.Errorf("批量插入產品失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("批量插入產品成功", zap.Int("count", len(params)))

	return nil
}

// FindAndHardDeleteDuplicatesForImport 實現了 Repository 介面的 FindAndHardDeleteDuplicatesForImport 方法
func (r *repository) FindAndHardDeleteDuplicatesForImport(ctx context.Context, params sqlc.FindAndHardDeleteDuplicatesForImportParams, tx sqlc.DBTX) ([]*sqlc.FindAndHardDeleteDuplicatesForImportRow, error) {
	logger := r.logger.Named("FindAndHardDeleteDuplicatesForImport")

	// 參數校驗
	if len(params.Pids) == 0 || len(params.ProductVats) == 0 || len(params.GroupIds) == 0 || len(params.ItemIds) == 0 || len(params.Names) == 0 {
		logger.Error("刪除重複產品失敗", zap.Error(errors.New("參數不完整")))
		return nil, errors.New("參數不完整")
	}

	querier := sqlc.New(tx)

	// 執行硬刪除
	deletedProducts, err := querier.FindAndHardDeleteDuplicatesForImport(ctx, params)
	if err != nil {
		logger.Error("刪除重複產品失敗", zap.Error(err))
		return nil, fmt.Errorf("刪除重複產品失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("刪除重複產品成功", zap.Int("count", len(deletedProducts)))

	return deletedProducts, nil
}

// convertToProduct 將 sqlc 生成的資料模型轉換為領域模型
//
// 參數:
// - sqlcProduct: 從資料庫查詢所得的原始資料模型
//
// 返回:
// - *models.Product: 轉換後的領域模型，適合業務邏輯處理
func convertToProduct(sqlcProduct *sqlc.Product) *models.Product {
	// 創建領域模型
	product := &models.Product{
		ID:             sqlcProduct.ID,
		PID:            sqlcProduct.Pid,
		ProductVAT:     sqlcProduct.ProductVat,
		ProductCompany: sqlcProduct.ProductCompany,
		GroupID:        sqlcProduct.GroupID,
		GroupName:      sqlcProduct.GroupName,
		ItemID:         sqlcProduct.ItemID,
		Name:           sqlcProduct.Name,
		IsDeleted:      sqlcProduct.IsDeleted,
		CreatedAt:      sqlcProduct.CreatedAt,
		UpdatedAt:      sqlcProduct.UpdatedAt,
		CreatedBy:      sqlcProduct.CreatedBy,
		UpdatedBy:      sqlcProduct.UpdatedBy,
	}

	// 處理可空欄位
	if sqlcProduct.Brand != nil {
		product.Brand = *sqlcProduct.Brand
	}

	if sqlcProduct.Category != nil {
		product.Category = *sqlcProduct.Category
	}

	if sqlcProduct.ProductNation != nil {
		product.ProductNation = *sqlcProduct.ProductNation
	}

	if sqlcProduct.UnitType != nil {
		product.UnitType = *sqlcProduct.UnitType
	}

	if sqlcProduct.Info != nil {
		product.Info = *sqlcProduct.Info
	}

	if sqlcProduct.Auth != nil {
		auth := *sqlcProduct.Auth
		product.Auth = &auth
	}

	if sqlcProduct.Price != nil {
		price := *sqlcProduct.Price
		product.Price = &price
	}

	if sqlcProduct.PriceInvoice != nil {
		priceInvoice := *sqlcProduct.PriceInvoice
		product.PriceInvoice = &priceInvoice
	}

	if sqlcProduct.BidPrice != nil {
		product.BidPrice = *sqlcProduct.BidPrice
	}

	if sqlcProduct.StepStart != nil {
		stepStart := *sqlcProduct.StepStart
		product.StepStart = &stepStart
	}

	if sqlcProduct.StepEnd != nil {
		stepEnd := *sqlcProduct.StepEnd
		product.StepEnd = &stepEnd
	}

	// 設定授權和出貨方式欄位
	product.AuthPC = int(sqlcProduct.AuthPc)
	product.AuthSVR = int(sqlcProduct.AuthSvr)
	product.AuthCAL = int(sqlcProduct.AuthCal)
	product.AuthMobile = int(sqlcProduct.AuthMobile)
	product.AuthCore = int(sqlcProduct.AuthCore)
	product.ShipAuth = int(sqlcProduct.ShipAuth)
	product.ShipBox = int(sqlcProduct.ShipBox)
	product.ShipDisk = int(sqlcProduct.ShipDisk)

	if sqlcProduct.Memo != nil {
		product.Memo = *sqlcProduct.Memo
	}

	return product
}
