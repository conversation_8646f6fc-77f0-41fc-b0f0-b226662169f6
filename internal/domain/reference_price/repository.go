package referencepricedomain

import (
	"context"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// Repository 定義參考價數據存取的介面
// 提供對參考價資料進行增刪改查和統計的抽象方法
type Repository interface {

	// GetByID 根據ID獲取參考價
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 參考價的唯一識別碼
	//
	// 返回:
	// - *models.ReferencePrice: 找到的參考價詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByID(ctx context.Context, id uint32) (*models.ReferencePrice, error)

	// GetByProjectAndProduct 根據專案ID和產品ID獲取參考價
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	// - productID: 產品ID
	//
	// 返回:
	// - *models.ReferencePrice: 找到的參考價詳情
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	GetByProjectAndProduct(ctx context.Context, projectID, productID uint32) (*models.ReferencePrice, error)

	// Create 創建參考價
	//
	// 參數:
	// - ctx: 操作上下文
	// - referencePrice: 包含參考價詳細資訊的結構體
	//
	// 返回:
	// - uint32: 新創建參考價的ID
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	Create(ctx context.Context, referencePrice *models.ReferencePrice) (uint32, error)

	// CreateWithTx 創建參考價（Transaction）
	//
	// 參數:
	// - ctx: 操作上下文
	// - referencePrice: 包含參考價詳細資訊的結構體
	// - tx: 資料庫事務，允許將此操作與其他操作整合在同一事務中
	//
	// 返回:
	// - uint32: 新創建參考價的ID
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	CreateWithTx(ctx context.Context, referencePrice *models.ReferencePrice, tx sqlc.DBTX) (uint32, error)

	// BatchCreate 批量創建參考價
	//
	// 參數:
	// - ctx: 操作上下文
	// - referencePrices: 包含多個參考價資料的陣列
	//
	// 返回:
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	BatchCreate(ctx context.Context, referencePrices []*models.ReferencePrice) error

	// Update 更新參考價
	//
	// 參數:
	// - ctx: 操作上下文
	// - referencePrice: 包含更新後參考價資訊的結構體
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	Update(ctx context.Context, referencePrice *models.ReferencePrice) error

	// UpdateWithTx 更新參考價（Transaction）
	//
	// 參數:
	// - ctx: 操作上下文
	// - referencePrice: 包含更新後參考價資訊的結構體
	// - tx: 資料庫事務，允許將此操作與其他操作整合在同一事務中
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	UpdateWithTx(ctx context.Context, referencePrice *models.ReferencePrice, tx sqlc.DBTX) error

	// UpdateStatus 更新參考價狀態
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 參考價ID
	// - status: 新狀態
	// - reviewRemark: 審核備註
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	UpdateStatus(ctx context.Context, id uint32, status sqlc.ReferencePriceStatus, reviewRemark string) error

	// List 根據條件查詢參考價列表（支持分頁和過濾）
	//
	// 參數:
	// - ctx: 操作上下文
	// - offset: 分頁起始位置
	// - limit: 每頁數量
	// - filters: 過濾條件，如專案ID、產品ID、狀態等
	//
	// 返回:
	// - []*models.ReferencePrice: 符合條件的參考價列表
	// - int: 總記錄數
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	List(ctx context.Context, offset, limit int32, filters models.ReferencePriceListParams) ([]*models.ReferencePrice, int, error)

	// CalculateStatsByProjectID 根據專案ID計算參考價統計
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	//
	// 返回:
	// - *models.ReferencePriceStats: 包含統計結果的結構體，如總數、狀態分佈等
	// - error: 可能的錯誤，如專案不存在或資料庫錯誤
	CalculateStatsByProjectID(ctx context.Context, projectID uint32) (*models.ReferencePriceStats, error)

	//TODO: 添加按產品類別查詢參考價的方法
	//TODO: 添加歷史參考價查詢方法
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("ReferencePrice"),
		querier: querier,
	}
}

func (r *repository) GetByID(ctx context.Context, id uint32) (*models.ReferencePrice, error) {

	logger := r.logger.Named("GetByID")
	logger.Debug("開始執行", zap.Uint32("id", id))

	// 調用 sqlc 生成的查詢函數
	sqlcReferencePrice, err := r.querier.GetReferencePriceByID(ctx, id)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, err
	}

	// 將 sqlc 的資料結構轉換為領域模型
	referencePrice := r.convertToReferencePrice(sqlcReferencePrice)
	logger.Debug("查詢成功")
	return referencePrice, nil
}

func (r *repository) GetByProjectAndProduct(ctx context.Context, projectID, productID uint32) (*models.ReferencePrice, error) {

	logger := r.logger.Named("GetByProjectAndProduct")
	logger.Debug("開始執行", zap.Uint32("projectID", projectID), zap.Uint32("productID", productID))

	// 調用 sqlc 生成的查詢函數
	sqlcReferencePrice, err := r.querier.GetReferencePriceByProjectAndProduct(ctx, sqlc.GetReferencePriceByProjectAndProductParams{
		ProjectID: projectID,
		ProductID: productID,
	})
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, err
	}

	// 將 sqlc 的資料結構轉換為領域模型
	referencePrice := r.convertToReferencePrice(sqlcReferencePrice)
	logger.Debug("查詢成功")
	return referencePrice, nil
}

func (r *repository) Create(ctx context.Context, referencePrice *models.ReferencePrice) (uint32, error) {

	logger := r.logger.Named("Create")
	logger.Debug("開始執行", zap.Any("referencePrice", referencePrice))

	// 調用 sqlc 生成的創建函數
	sqlcParams := sqlc.CreateReferencePriceParams{
		ProjectID: referencePrice.ProjectID,
		ProductID: referencePrice.ProductID,
		Reasonability: sqlc.NullPriceReasonability{
			PriceReasonability: referencePrice.Reasonability,
			Valid:              referencePrice.Reasonability != "",
		},
		Status:       referencePrice.Status,
		Principle:    &referencePrice.Principle,
		SpoInterval:  &referencePrice.SPOInterval,
		ReviewRemark: &referencePrice.ReviewRemark,
	}

	if referencePrice.OriginalReferencePrice != nil {
		sqlcParams.OriginalReferencePrice = *referencePrice.OriginalReferencePrice
	}

	if referencePrice.SPOReferencePrice != nil {
		sqlcParams.SpoReferencePrice = *referencePrice.SPOReferencePrice
	}

	if referencePrice.VendorBidPrice != nil {
		sqlcParams.VendorBidPrice = *referencePrice.VendorBidPrice
	}

	if referencePrice.VendorBidPricePercentage != nil {
		sqlcParams.VendorBidPricePercentage = *referencePrice.VendorBidPricePercentage
	}

	if referencePrice.CISAReferencePrice != nil {
		sqlcParams.CisaReferencePrice = *referencePrice.CISAReferencePrice
	}

	if referencePrice.CISAReferencePricePercentage != nil {
		sqlcParams.CisaReferencePricePercentage = *referencePrice.CISAReferencePricePercentage
	}

	if referencePrice.CreatedBy != nil {
		sqlcParams.CreatedBy = *referencePrice.CreatedBy
	}

	if referencePrice.UpdatedBy != nil {
		sqlcParams.UpdatedBy = *referencePrice.UpdatedBy
	}

	sqlcReferencePrice, err := r.querier.CreateReferencePrice(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, err
	}

	logger.Debug("創建成功", zap.Uint32("id", sqlcReferencePrice.ID))
	return sqlcReferencePrice.ID, nil
}

func (r *repository) CreateWithTx(ctx context.Context, referencePrice *models.ReferencePrice, tx sqlc.DBTX) (uint32, error) {

	logger := r.logger.Named("CreateWithTx")
	logger.Debug("開始執行", zap.Any("referencePrice", referencePrice))

	// 調用 sqlc 生成的創建函數
	sqlcParams := sqlc.CreateReferencePriceParams{
		ProjectID: referencePrice.ProjectID,
		ProductID: referencePrice.ProductID,
		Reasonability: sqlc.NullPriceReasonability{
			PriceReasonability: referencePrice.Reasonability,
			Valid:              referencePrice.Reasonability != "",
		},
		Status:       referencePrice.Status,
		Principle:    &referencePrice.Principle,
		SpoInterval:  &referencePrice.SPOInterval,
		ReviewRemark: &referencePrice.ReviewRemark,
	}

	if referencePrice.OriginalReferencePrice != nil {
		sqlcParams.OriginalReferencePrice = *referencePrice.OriginalReferencePrice
	}

	if referencePrice.SPOReferencePrice != nil {
		sqlcParams.SpoReferencePrice = *referencePrice.SPOReferencePrice
	}

	if referencePrice.VendorBidPrice != nil {
		sqlcParams.VendorBidPrice = *referencePrice.VendorBidPrice
	}

	if referencePrice.VendorBidPricePercentage != nil {
		sqlcParams.VendorBidPricePercentage = *referencePrice.VendorBidPricePercentage
	}

	if referencePrice.CISAReferencePrice != nil {
		sqlcParams.CisaReferencePrice = *referencePrice.CISAReferencePrice
	}

	if referencePrice.CISAReferencePricePercentage != nil {
		sqlcParams.CisaReferencePricePercentage = *referencePrice.CISAReferencePricePercentage
	}

	if referencePrice.CreatedBy != nil {
		sqlcParams.CreatedBy = *referencePrice.CreatedBy
	}

	if referencePrice.UpdatedBy != nil {
		sqlcParams.UpdatedBy = *referencePrice.UpdatedBy
	}

	sqlcReferencePrice, err := sqlc.New(tx).CreateReferencePrice(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, err
	}

	logger.Debug("創建成功", zap.Uint32("id", sqlcReferencePrice.ID))
	return sqlcReferencePrice.ID, nil
}

func (r *repository) BatchCreate(ctx context.Context, referencePrices []*models.ReferencePrice) error {

	logger := r.logger.Named("BatchCreate")
	logger.Debug("開始執行", zap.Int("count", len(referencePrices)))

	// 調用 sqlc 生成的批量創建函數
	sqlcParams := make([]sqlc.BatchCreateReferencePricesParams, 0, len(referencePrices))
	for _, referencePrice := range referencePrices {
		params := sqlc.BatchCreateReferencePricesParams{
			ProjectID: referencePrice.ProjectID,
			ProductID: referencePrice.ProductID,
			Reasonability: sqlc.NullPriceReasonability{
				PriceReasonability: referencePrice.Reasonability,
				Valid:              referencePrice.Reasonability != "",
			},
			Status:      referencePrice.Status,
			Principle:   &referencePrice.Principle,
			SpoInterval: &referencePrice.SPOInterval,
		}

		if referencePrice.OriginalReferencePrice != nil {
			params.OriginalReferencePrice = *referencePrice.OriginalReferencePrice
		}

		if referencePrice.SPOReferencePrice != nil {
			params.SpoReferencePrice = *referencePrice.SPOReferencePrice
		}

		if referencePrice.VendorBidPrice != nil {
			params.VendorBidPrice = *referencePrice.VendorBidPrice
		}

		if referencePrice.VendorBidPricePercentage != nil {
			params.VendorBidPricePercentage = *referencePrice.VendorBidPricePercentage
		}

		if referencePrice.CISAReferencePrice != nil {
			params.CisaReferencePrice = *referencePrice.CISAReferencePrice
		}

		if referencePrice.CISAReferencePricePercentage != nil {
			params.CisaReferencePricePercentage = *referencePrice.CISAReferencePricePercentage
		}

		if referencePrice.CreatedBy != nil {
			params.CreatedBy = *referencePrice.CreatedBy
		}

		if referencePrice.UpdatedBy != nil {
			params.UpdatedBy = *referencePrice.UpdatedBy
		}

		sqlcParams = append(sqlcParams, params)
	}

	if _, err := r.querier.BatchCreateReferencePrices(ctx, sqlcParams); err != nil {
		logger.Error("批量創建失敗", zap.Error(err))
		return err
	}

	logger.Debug("批量創建成功")
	return nil
}

func (r *repository) BatchCreateWithTx(ctx context.Context, referencePrices []*models.ReferencePrice, tx sqlc.DBTX) error {
	logger := r.logger.Named("BatchCreateWithTx")
	logger.Debug("開始執行", zap.Int("count", len(referencePrices)))

	// 調用 sqlc 生成的批量創建函數
	sqlcParams := make([]sqlc.BatchCreateReferencePricesParams, 0, len(referencePrices))
	for _, referencePrice := range referencePrices {
		params := sqlc.BatchCreateReferencePricesParams{
			ProjectID: referencePrice.ProjectID,
			ProductID: referencePrice.ProductID,
			Reasonability: sqlc.NullPriceReasonability{
				PriceReasonability: referencePrice.Reasonability,
				Valid:              referencePrice.Reasonability != "",
			},
			Status:      referencePrice.Status,
			Principle:   &referencePrice.Principle,
			SpoInterval: &referencePrice.SPOInterval,
		}

		if referencePrice.OriginalReferencePrice != nil {
			params.OriginalReferencePrice = *referencePrice.OriginalReferencePrice
		}

		if referencePrice.SPOReferencePrice != nil {
			params.SpoReferencePrice = *referencePrice.SPOReferencePrice
		}

		if referencePrice.VendorBidPrice != nil {
			params.VendorBidPrice = *referencePrice.VendorBidPrice
		}

		if referencePrice.VendorBidPricePercentage != nil {
			params.VendorBidPricePercentage = *referencePrice.VendorBidPricePercentage
		}

		if referencePrice.CISAReferencePrice != nil {
			params.CisaReferencePrice = *referencePrice.CISAReferencePrice
		}

		if referencePrice.CISAReferencePricePercentage != nil {
			params.CisaReferencePricePercentage = *referencePrice.CISAReferencePricePercentage
		}

		if referencePrice.CreatedBy != nil {
			params.CreatedBy = *referencePrice.CreatedBy
		}

		if referencePrice.UpdatedBy != nil {
			params.UpdatedBy = *referencePrice.UpdatedBy
		}

		sqlcParams = append(sqlcParams, params)
	}

	if _, err := sqlc.New(tx).BatchCreateReferencePrices(ctx, sqlcParams); err != nil {
		logger.Error("批量創建失敗", zap.Error(err))
		return err
	}

	logger.Debug("批量創建成功")
	return nil
}

func (r *repository) Update(ctx context.Context, referencePrice *models.ReferencePrice) error {

	logger := r.logger.Named("Update")
	logger.Debug("開始執行", zap.Any("referencePrice", referencePrice))

	// 調用 sqlc 生成的更新函數
	sqlcParams := sqlc.UpdateReferencePriceParams{
		ID:           referencePrice.ID,
		Principle:    &referencePrice.Principle,
		SpoInterval:  &referencePrice.SPOInterval,
		ReviewRemark: &referencePrice.ReviewRemark,
		Reasonability: sqlc.NullPriceReasonability{
			PriceReasonability: referencePrice.Reasonability,
			Valid:              referencePrice.Reasonability != "",
		},
		Status: sqlc.NullReferencePriceStatus{
			ReferencePriceStatus: referencePrice.Status,
			Valid:                referencePrice.Status != "",
		},
	}

	if referencePrice.OriginalReferencePrice != nil {
		sqlcParams.OriginalReferencePrice = *referencePrice.OriginalReferencePrice
	}

	if referencePrice.SPOReferencePrice != nil {
		sqlcParams.SpoReferencePrice = *referencePrice.SPOReferencePrice
	}

	if referencePrice.VendorBidPrice != nil {
		sqlcParams.VendorBidPrice = *referencePrice.VendorBidPrice
	}

	if referencePrice.VendorBidPricePercentage != nil {
		sqlcParams.VendorBidPricePercentage = *referencePrice.VendorBidPricePercentage
	}

	if referencePrice.CISAReferencePrice != nil {
		sqlcParams.CisaReferencePrice = *referencePrice.CISAReferencePrice
	}

	if referencePrice.CISAReferencePricePercentage != nil {
		sqlcParams.CisaReferencePricePercentage = *referencePrice.CISAReferencePricePercentage
	}

	if referencePrice.UpdatedBy != nil {
		sqlcParams.UpdatedBy = *referencePrice.UpdatedBy
	}

	if _, err := r.querier.UpdateReferencePrice(ctx, sqlcParams); err != nil {
		logger.Error("更新失敗", zap.Error(err))
		return err
	}

	logger.Debug("更新成功")
	return nil
}

func (r *repository) UpdateWithTx(ctx context.Context, referencePrice *models.ReferencePrice, tx sqlc.DBTX) error {
	logger := r.logger.Named("UpdateWithTx")
	logger.Debug("開始執行", zap.Any("referencePrice", referencePrice))

	// 調用 sqlc 生成的更新函數
	sqlcParams := sqlc.UpdateReferencePriceParams{
		ID:           referencePrice.ID,
		Principle:    &referencePrice.Principle,
		SpoInterval:  &referencePrice.SPOInterval,
		ReviewRemark: &referencePrice.ReviewRemark,
		Reasonability: sqlc.NullPriceReasonability{
			PriceReasonability: referencePrice.Reasonability,
			Valid:              referencePrice.Reasonability != "",
		},
		Status: sqlc.NullReferencePriceStatus{
			ReferencePriceStatus: referencePrice.Status,
			Valid:                referencePrice.Status != "",
		},
	}

	if referencePrice.OriginalReferencePrice != nil {
		sqlcParams.OriginalReferencePrice = *referencePrice.OriginalReferencePrice
	}

	if referencePrice.SPOReferencePrice != nil {
		sqlcParams.SpoReferencePrice = *referencePrice.SPOReferencePrice
	}

	if referencePrice.VendorBidPrice != nil {
		sqlcParams.VendorBidPrice = *referencePrice.VendorBidPrice
	}

	if referencePrice.VendorBidPricePercentage != nil {
		sqlcParams.VendorBidPricePercentage = *referencePrice.VendorBidPricePercentage
	}

	if referencePrice.CISAReferencePrice != nil {
		sqlcParams.CisaReferencePrice = *referencePrice.CISAReferencePrice
	}

	if referencePrice.CISAReferencePricePercentage != nil {
		sqlcParams.CisaReferencePricePercentage = *referencePrice.CISAReferencePricePercentage
	}

	if referencePrice.UpdatedBy != nil {
		sqlcParams.UpdatedBy = *referencePrice.UpdatedBy
	}

	if _, err := sqlc.New(tx).UpdateReferencePrice(ctx, sqlcParams); err != nil {
		logger.Error("更新失敗", zap.Error(err))
		return err
	}

	logger.Debug("更新成功")
	return nil
}

func (r *repository) UpdateStatus(ctx context.Context, id uint32, status sqlc.ReferencePriceStatus, reviewRemark string) error {

	logger := r.logger.Named("UpdateStatus")
	logger.Debug("開始執行", zap.Uint32("id", id), zap.String("status", string(status)))

	// 調用 sqlc 生成的更新函數
	if _, err := r.querier.UpdateReferencePriceStatus(ctx, sqlc.UpdateReferencePriceStatusParams{
		ID:           id,
		Column2:      status,
		ReviewRemark: &reviewRemark,
	}); err != nil {
		logger.Error("更新失敗", zap.Error(err))
		return err
	}

	logger.Debug("更新成功")
	return nil
}

func (r *repository) List(ctx context.Context, offset, limit int32, filters models.ReferencePriceListParams) ([]*models.ReferencePrice, int, error) {

	logger := r.logger.Named("List")
	logger.Debug("開始執行", zap.Int32("offset", offset), zap.Int32("limit", limit), zap.Any("filters", filters))

	// 構造 sqlc 參數
	params := sqlc.ListReferencePricesParams{
		OffsetVal: &offset,
		LimitVal:  &limit,
		SortDir:   &filters.SortDir,
	}

	if filters.ProjectID != 0 {
		projectID := int64(filters.ProjectID)
		params.ProjectID = &projectID
	}

	if filters.ProductID != 0 {
		productID := int64(filters.ProductID)
		params.ProductID = &productID
	}

	if filters.Principle != "" {
		principle := filters.Principle
		params.Principle = &principle
	}

	if filters.Status != "" {
		status := filters.Status
		params.Status = &status
	}

	if filters.Reasonability != "" {
		reasonability := filters.Reasonability
		params.Reasonability = &reasonability
	}

	if filters.GroupID != 0 {
		groupID := int64(filters.GroupID)
		params.GroupID = &groupID
	}

	if filters.SearchTerm != "" {
		searchTerm := filters.SearchTerm
		params.SearchTerm = &searchTerm
	}

	if filters.SortBy != "" {
		sortBy := filters.SortBy
		params.SortBy = &sortBy
	}

	if filters.SortDir != "" {
		sortDir := filters.SortDir
		params.SortDir = &sortDir
	}

	// 執行查詢
	sqlcReferencePrices, err := r.querier.ListReferencePrices(ctx, params)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, 0, err
	}

	// 轉換為領域模型
	referencePrices := make([]*models.ReferencePrice, 0, len(sqlcReferencePrices))
	for _, sqlcReferencePrice := range sqlcReferencePrices {
		referencePrices = append(referencePrices, r.convertToReferencePrice(sqlcReferencePrice))
	}

	logger.Debug("查詢成功", zap.Int("count", len(referencePrices)))
	return referencePrices, len(referencePrices), nil
}

func (r *repository) CalculateStatsByProjectID(ctx context.Context, projectID uint32) (*models.ReferencePriceStats, error) {

	logger := r.logger.Named("CalculateStatsByProjectID")
	logger.Debug("開始執行", zap.Uint32("projectID", projectID))

	// 執行查詢
	stats, err := r.querier.CalculateReferencePriceStatsByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("計算參考價統計失敗", zap.Error(err))
		return nil, err
	}

	// 將 sqlc 的資料結構轉換為領域模型
	referencePriceStats := r.convertToReferencePriceStats(stats)
	logger.Debug("計算參考價統計成功")
	return referencePriceStats, nil
}

func (r *repository) convertToReferencePrice(sqlcReferencePrice *sqlc.ReferencePrice) *models.ReferencePrice {

	price := &models.ReferencePrice{
		ID:                           sqlcReferencePrice.ID,
		ProjectID:                    sqlcReferencePrice.ProjectID,
		ProductID:                    sqlcReferencePrice.ProductID,
		OriginalReferencePrice:       &sqlcReferencePrice.OriginalReferencePrice,
		SPOReferencePrice:            &sqlcReferencePrice.SpoReferencePrice,
		VendorBidPrice:               &sqlcReferencePrice.VendorBidPrice,
		VendorBidPricePercentage:     &sqlcReferencePrice.VendorBidPricePercentage,
		CISAReferencePrice:           &sqlcReferencePrice.CisaReferencePrice,
		CISAReferencePricePercentage: &sqlcReferencePrice.CisaReferencePricePercentage,
		Reasonability:                sqlcReferencePrice.Reasonability.PriceReasonability,
	}

	if sqlcReferencePrice.Principle != nil {
		price.Principle = *sqlcReferencePrice.Principle
	}

	if sqlcReferencePrice.SpoInterval != nil {
		price.SPOInterval = *sqlcReferencePrice.SpoInterval
	}

	if sqlcReferencePrice.ReviewRemark != nil {
		price.ReviewRemark = *sqlcReferencePrice.ReviewRemark
	}

	return price
}

func (r *repository) convertToReferencePriceStats(stats []*sqlc.CalculateReferencePriceStatsByProjectIDRow) *models.ReferencePriceStats {
	return &models.ReferencePriceStats{
		TotalCount:           int(stats[0].TotalCount),
		PrincipleOneCounts:   int(stats[0].PrincipleCount),
		PrincipleTwoCounts:   int(stats[1].PrincipleCount),
		PrincipleThreeCounts: int(stats[2].PrincipleCount),
		PrincipleFourCounts:  int(stats[3].PrincipleCount),
		ReasonableCounts:     int(stats[0].TotalReasonableCount),
		UnreasonableCounts:   int(stats[0].TotalUnreasonableCount),
		UnclassifiedCounts:   int(stats[0].TotalUnclassifiedCount),
		ConfirmedCounts:      int(stats[0].ConfirmedCount),
		PendingCounts:        int(stats[0].PendingCount),
		UnconfirmedCounts:    int(stats[0].UnconfirmedCount),
		ExcludedCounts:       int(stats[0].ExcludedCount),
		SPOAdjustedCounts:    int(stats[0].Count),
	}
}
