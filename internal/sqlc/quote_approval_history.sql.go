// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: quote_approval_history.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

type BatchCreateQuoteApprovalHistoriesParams struct {
	QuoteID   uint32      `json:"quoteId"`
	OldStatus QuoteStatus `json:"oldStatus"`
	NewStatus QuoteStatus `json:"newStatus"`
	Remark    *string     `json:"remark"`
	CreatedBy uint32      `json:"createdBy"`
	BatchID   uint32      `json:"batchId"`
}

const countQuoteApprovalHistoriesByQuote = `-- name: CountQuoteApprovalHistoriesByQuote :one
SELECT COUNT(*) FROM quote_approval_history
WHERE quote_id = $1
`

// 計算特定報價的審核歷史數量
func (q *Queries) CountQuoteApprovalHistoriesByQuote(ctx context.Context, quoteID uint32) (int64, error) {
	row := q.db.QueryRow(ctx, countQuoteApprovalHistoriesByQuote, quoteID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createQuoteApprovalHistory = `-- name: CreateQuoteApprovalHistory :one
INSERT INTO quote_approval_history (
    quote_id,
    old_status,
    new_status,
    remark,
    created_by,
    batch_id
) VALUES (
             $1, $2, $3, $4, $5, $6
         )
RETURNING id, quote_id, old_status, new_status, remark, created_at, created_by, batch_id
`

type CreateQuoteApprovalHistoryParams struct {
	QuoteID   uint32      `json:"quoteId"`
	OldStatus QuoteStatus `json:"oldStatus"`
	NewStatus QuoteStatus `json:"newStatus"`
	Remark    *string     `json:"remark"`
	CreatedBy uint32      `json:"createdBy"`
	BatchID   uint32      `json:"batchId"`
}

// 創建審核歷史記錄
func (q *Queries) CreateQuoteApprovalHistory(ctx context.Context, arg CreateQuoteApprovalHistoryParams) (*QuoteApprovalHistory, error) {
	row := q.db.QueryRow(ctx, createQuoteApprovalHistory,
		arg.QuoteID,
		arg.OldStatus,
		arg.NewStatus,
		arg.Remark,
		arg.CreatedBy,
		arg.BatchID,
	)
	var i QuoteApprovalHistory
	err := row.Scan(
		&i.ID,
		&i.QuoteID,
		&i.OldStatus,
		&i.NewStatus,
		&i.Remark,
		&i.CreatedAt,
		&i.CreatedBy,
		&i.BatchID,
	)
	return &i, err
}

const createQuoteApprovalHistoryWithTx = `-- name: CreateQuoteApprovalHistoryWithTx :one
INSERT INTO quote_approval_history (
    quote_id,
    old_status,
    new_status,
    remark,
    created_by,
    batch_id
) VALUES (
             $1, $2, $3, $4, $5, $6
         )
RETURNING id, quote_id, old_status, new_status, remark, created_at, created_by, batch_id
`

type CreateQuoteApprovalHistoryWithTxParams struct {
	QuoteID   uint32      `json:"quoteId"`
	OldStatus QuoteStatus `json:"oldStatus"`
	NewStatus QuoteStatus `json:"newStatus"`
	Remark    *string     `json:"remark"`
	CreatedBy uint32      `json:"createdBy"`
	BatchID   uint32      `json:"batchId"`
}

// 在事務中創建審核歷史記錄
func (q *Queries) CreateQuoteApprovalHistoryWithTx(ctx context.Context, arg CreateQuoteApprovalHistoryWithTxParams) (*QuoteApprovalHistory, error) {
	row := q.db.QueryRow(ctx, createQuoteApprovalHistoryWithTx,
		arg.QuoteID,
		arg.OldStatus,
		arg.NewStatus,
		arg.Remark,
		arg.CreatedBy,
		arg.BatchID,
	)
	var i QuoteApprovalHistory
	err := row.Scan(
		&i.ID,
		&i.QuoteID,
		&i.OldStatus,
		&i.NewStatus,
		&i.Remark,
		&i.CreatedAt,
		&i.CreatedBy,
		&i.BatchID,
	)
	return &i, err
}

const deleteQuoteApprovalHistory = `-- name: DeleteQuoteApprovalHistory :exec
DELETE FROM quote_approval_history
WHERE id = $1
`

// 刪除審核歷史記錄（通常不需要這個功能，因為審核歷史應該保留，但為API完整性提供）
func (q *Queries) DeleteQuoteApprovalHistory(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteQuoteApprovalHistory, id)
	return err
}

const getQuoteApprovalHistoryByID = `-- name: GetQuoteApprovalHistoryByID :one
SELECT id, quote_id, old_status, new_status, remark, created_at, created_by, batch_id FROM quote_approval_history
WHERE id = $1
`

// 根據ID獲取單筆審核歷史
func (q *Queries) GetQuoteApprovalHistoryByID(ctx context.Context, id uint32) (*QuoteApprovalHistory, error) {
	row := q.db.QueryRow(ctx, getQuoteApprovalHistoryByID, id)
	var i QuoteApprovalHistory
	err := row.Scan(
		&i.ID,
		&i.QuoteID,
		&i.OldStatus,
		&i.NewStatus,
		&i.Remark,
		&i.CreatedAt,
		&i.CreatedBy,
		&i.BatchID,
	)
	return &i, err
}

const listApprovalHistoriesWithFilters = `-- name: ListApprovalHistoriesWithFilters :many
SELECT id, quote_id, old_status, new_status, remark, created_at, created_by, batch_id FROM quote_approval_history
WHERE
  -- 使用COALESCE處理空值過濾條件
    (COALESCE($1::INTEGER, 0) = 0 OR quote_id = $1::INTEGER) AND
    (COALESCE($2::INTEGER, 0) = 0 OR batch_id = $2::INTEGER) AND
    (COALESCE($3::INTEGER, 0) = 0 OR created_by = $3::INTEGER) AND
    ($4 = '' OR old_status = $4::quote_status) AND
    ($5 = '' OR new_status = $5::quote_status) AND
    ($6::TIMESTAMPTZ IS NULL OR created_at >= $6::TIMESTAMPTZ) AND
    ($7::TIMESTAMPTZ IS NULL OR created_at <= $7::TIMESTAMPTZ)
ORDER BY
    CASE
        WHEN $8::TEXT = 'created_at' AND $9::TEXT = 'asc' THEN created_at
        END ASC NULLS LAST,
    CASE
        WHEN $8::TEXT = 'created_at' AND $9::TEXT = 'desc' THEN created_at
        END DESC NULLS LAST,
    -- 預設排序
    CASE
        WHEN $8::TEXT = '' OR $8::TEXT IS NULL THEN created_at
        END DESC
LIMIT COALESCE($11::INTEGER, 50)
    OFFSET COALESCE($10::INTEGER, 0)
`

type ListApprovalHistoriesWithFiltersParams struct {
	QuoteID       *int32             `json:"quoteId"`
	BatchID       *int32             `json:"batchId"`
	CreatedBy     *int32             `json:"createdBy"`
	OldStatus     *string            `json:"oldStatus"`
	NewStatus     *string            `json:"newStatus"`
	CreatedAfter  pgtype.Timestamptz `json:"createdAfter"`
	CreatedBefore pgtype.Timestamptz `json:"createdBefore"`
	SortBy        *string            `json:"sortBy"`
	SortDir       *string            `json:"sortDir"`
	Offset        *int32             `json:"offset"`
	Limit         *int32             `json:"limit"`
}

// 根據多種條件查詢審核歷史記錄
// 使用可選參數的高效過濾方法
// 使用動態排序
// 分頁參數
func (q *Queries) ListApprovalHistoriesWithFilters(ctx context.Context, arg ListApprovalHistoriesWithFiltersParams) ([]*QuoteApprovalHistory, error) {
	rows, err := q.db.Query(ctx, listApprovalHistoriesWithFilters,
		arg.QuoteID,
		arg.BatchID,
		arg.CreatedBy,
		arg.OldStatus,
		arg.NewStatus,
		arg.CreatedAfter,
		arg.CreatedBefore,
		arg.SortBy,
		arg.SortDir,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*QuoteApprovalHistory{}
	for rows.Next() {
		var i QuoteApprovalHistory
		if err := rows.Scan(
			&i.ID,
			&i.QuoteID,
			&i.OldStatus,
			&i.NewStatus,
			&i.Remark,
			&i.CreatedAt,
			&i.CreatedBy,
			&i.BatchID,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listQuoteApprovalHistoriesByBatchID = `-- name: ListQuoteApprovalHistoriesByBatchID :many
SELECT id, quote_id, old_status, new_status, remark, created_at, created_by, batch_id FROM quote_approval_history
WHERE batch_id = $1
ORDER BY created_at DESC
`

// 根據批次ID獲取審核歷史，按照創建時間降序排序
func (q *Queries) ListQuoteApprovalHistoriesByBatchID(ctx context.Context, batchID uint32) ([]*QuoteApprovalHistory, error) {
	rows, err := q.db.Query(ctx, listQuoteApprovalHistoriesByBatchID, batchID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*QuoteApprovalHistory{}
	for rows.Next() {
		var i QuoteApprovalHistory
		if err := rows.Scan(
			&i.ID,
			&i.QuoteID,
			&i.OldStatus,
			&i.NewStatus,
			&i.Remark,
			&i.CreatedAt,
			&i.CreatedBy,
			&i.BatchID,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listQuoteApprovalHistoriesByQuoteID = `-- name: ListQuoteApprovalHistoriesByQuoteID :many
SELECT id, quote_id, old_status, new_status, remark, created_at, created_by, batch_id FROM quote_approval_history
WHERE quote_id = $1
ORDER BY created_at DESC
`

// 根據報價ID獲取審核歷史，按照創建時間降序排序
func (q *Queries) ListQuoteApprovalHistoriesByQuoteID(ctx context.Context, quoteID uint32) ([]*QuoteApprovalHistory, error) {
	rows, err := q.db.Query(ctx, listQuoteApprovalHistoriesByQuoteID, quoteID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*QuoteApprovalHistory{}
	for rows.Next() {
		var i QuoteApprovalHistory
		if err := rows.Scan(
			&i.ID,
			&i.QuoteID,
			&i.OldStatus,
			&i.NewStatus,
			&i.Remark,
			&i.CreatedAt,
			&i.CreatedBy,
			&i.BatchID,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
