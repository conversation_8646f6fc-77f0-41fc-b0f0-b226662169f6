// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: registration_requests.sql

package sqlc

import (
	"context"
)

const checkBusinessNoExists = `-- name: CheckBusinessNoExists :one
SELECT EXISTS (
    SELECT 1 FROM (
                      SELECT unified_business_no FROM registration_requests
                      UNION ALL
                      SELECT unified_business_no FROM companies
                  ) AS accounts
    WHERE unified_business_no = $1
)
`

// 檢查統一編號是否已存在（檢查註冊申請和已建立的公司）
func (q *Queries) CheckBusinessNoExists(ctx context.Context, dollar_1 *string) (bool, error) {
	row := q.db.QueryRow(ctx, checkBusinessNoExists, dollar_1)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const checkEmailExists = `-- name: CheckEmailExists :one
SELECT EXISTS (
    SELECT 1 FROM (
                      SELECT email FROM registration_requests
                      UNION ALL
                      SELECT email FROM users
                  ) AS accounts
    WHERE email = $1
)
`

// 檢查電子郵件是否已存在（檢查註冊申請和已建立的使用者）
func (q *Queries) CheckEmailExists(ctx context.Context, dollar_1 *string) (bool, error) {
	row := q.db.QueryRow(ctx, checkEmailExists, dollar_1)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const countPendingRegistrationRequests = `-- name: CountPendingRegistrationRequests :one
SELECT COUNT(*)
FROM registration_requests
WHERE status = '註冊待審'
`

// 計算待審核的註冊申請總數
func (q *Queries) CountPendingRegistrationRequests(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, countPendingRegistrationRequests)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countRegistrationRequests = `-- name: CountRegistrationRequests :one
SELECT COUNT(*)
FROM registration_requests
WHERE
    CASE WHEN $1::text != '' THEN status = $1::registration_requests_type ELSE TRUE END AND
    CASE WHEN $2::text != '' THEN
             company_name ILIKE '%' || $2 || '%' OR
             unified_business_no ILIKE '%' || $2 || '%' OR
             contact_person ILIKE '%' || $2 || '%' OR
             email ILIKE '%' || $2 || '%'
         ELSE TRUE END
`

type CountRegistrationRequestsParams struct {
	Status     string `json:"status"`
	SearchTerm string `json:"searchTerm"`
}

// 計算符合條件的註冊申請總數，用於分頁
func (q *Queries) CountRegistrationRequests(ctx context.Context, arg CountRegistrationRequestsParams) (int64, error) {
	row := q.db.QueryRow(ctx, countRegistrationRequests, arg.Status, arg.SearchTerm)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createRegistrationRequest = `-- name: CreateRegistrationRequest :one
INSERT INTO registration_requests (
    unified_business_no,
    company_name,
    company_type,
    address,
    company_owner,
    contact_person,
    job_title,
    phone,
    mobile,
    email,
    backup_email,
    user_role,
    password_hash,
    remark
) VALUES (
             $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
         )
RETURNING id, unified_business_no, company_name, company_type, address, company_owner,
    contact_person, job_title, phone, mobile, email, backup_email, user_role,
    password_hash, status, remark, review_remark, created_at, updated_at, reviewed_at, reviewed_by
`

type CreateRegistrationRequestParams struct {
	UnifiedBusinessNo string          `json:"unifiedBusinessNo"`
	CompanyName       string          `json:"companyName"`
	CompanyType       NullCompanyType `json:"companyType"`
	Address           *string         `json:"address"`
	CompanyOwner      string          `json:"companyOwner"`
	ContactPerson     string          `json:"contactPerson"`
	JobTitle          *string         `json:"jobTitle"`
	Phone             *string         `json:"phone"`
	Mobile            *string         `json:"mobile"`
	Email             string          `json:"email"`
	BackupEmail       *string         `json:"backupEmail"`
	UserRole          UserRole        `json:"userRole"`
	PasswordHash      string          `json:"passwordHash"`
	Remark            *string         `json:"remark"`
}

// 新增註冊申請，返回完整新增的記錄
func (q *Queries) CreateRegistrationRequest(ctx context.Context, arg CreateRegistrationRequestParams) (*RegistrationRequest, error) {
	row := q.db.QueryRow(ctx, createRegistrationRequest,
		arg.UnifiedBusinessNo,
		arg.CompanyName,
		arg.CompanyType,
		arg.Address,
		arg.CompanyOwner,
		arg.ContactPerson,
		arg.JobTitle,
		arg.Phone,
		arg.Mobile,
		arg.Email,
		arg.BackupEmail,
		arg.UserRole,
		arg.PasswordHash,
		arg.Remark,
	)
	var i RegistrationRequest
	err := row.Scan(
		&i.ID,
		&i.UnifiedBusinessNo,
		&i.CompanyName,
		&i.CompanyType,
		&i.Address,
		&i.CompanyOwner,
		&i.ContactPerson,
		&i.JobTitle,
		&i.Phone,
		&i.Mobile,
		&i.Email,
		&i.BackupEmail,
		&i.UserRole,
		&i.PasswordHash,
		&i.Status,
		&i.Remark,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.ReviewedBy,
	)
	return &i, err
}

const deleteRegistrationRequest = `-- name: DeleteRegistrationRequest :exec
DELETE FROM registration_requests
WHERE id = $1
`

// 刪除註冊申請（通常在審核通過後，資料已經轉移到正式表中）
func (q *Queries) DeleteRegistrationRequest(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteRegistrationRequest, id)
	return err
}

const getPendingRegistrationRequests = `-- name: GetPendingRegistrationRequests :many
SELECT
    id, unified_business_no, company_name, company_type, address, company_owner,
    contact_person, job_title, phone, mobile, email, backup_email, user_role,
    password_hash, status, remark, review_remark, created_at, updated_at, reviewed_at, reviewed_by
FROM registration_requests
WHERE status = '註冊待審'
ORDER BY created_at ASC
LIMIT CASE
          WHEN $2::int > 0 THEN $2::int
          WHEN $2 IS NULL THEN NULL
          ELSE 2147483647
    END
    OFFSET $1::int
`

type GetPendingRegistrationRequestsParams struct {
	OffsetVal int32 `json:"offsetVal"`
	LimitVal  int32 `json:"limitVal"`
}

// 列出所有待審核的註冊申請，按創建時間升序排列（先進先出）
func (q *Queries) GetPendingRegistrationRequests(ctx context.Context, arg GetPendingRegistrationRequestsParams) ([]*RegistrationRequest, error) {
	rows, err := q.db.Query(ctx, getPendingRegistrationRequests, arg.OffsetVal, arg.LimitVal)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*RegistrationRequest{}
	for rows.Next() {
		var i RegistrationRequest
		if err := rows.Scan(
			&i.ID,
			&i.UnifiedBusinessNo,
			&i.CompanyName,
			&i.CompanyType,
			&i.Address,
			&i.CompanyOwner,
			&i.ContactPerson,
			&i.JobTitle,
			&i.Phone,
			&i.Mobile,
			&i.Email,
			&i.BackupEmail,
			&i.UserRole,
			&i.PasswordHash,
			&i.Status,
			&i.Remark,
			&i.ReviewRemark,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ReviewedAt,
			&i.ReviewedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRegistrationRequestByEmail = `-- name: GetRegistrationRequestByEmail :one
SELECT
    id, unified_business_no, company_name, company_type, address, company_owner,
    contact_person, job_title, phone, mobile, email, backup_email, user_role,
    password_hash, status, remark, review_remark, created_at, updated_at, reviewed_at, reviewed_by
FROM registration_requests
WHERE email = $1
LIMIT 1
`

// 根據電子郵件獲取註冊申請
func (q *Queries) GetRegistrationRequestByEmail(ctx context.Context, email string) (*RegistrationRequest, error) {
	row := q.db.QueryRow(ctx, getRegistrationRequestByEmail, email)
	var i RegistrationRequest
	err := row.Scan(
		&i.ID,
		&i.UnifiedBusinessNo,
		&i.CompanyName,
		&i.CompanyType,
		&i.Address,
		&i.CompanyOwner,
		&i.ContactPerson,
		&i.JobTitle,
		&i.Phone,
		&i.Mobile,
		&i.Email,
		&i.BackupEmail,
		&i.UserRole,
		&i.PasswordHash,
		&i.Status,
		&i.Remark,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.ReviewedBy,
	)
	return &i, err
}

const getRegistrationRequestByID = `-- name: GetRegistrationRequestByID :one
SELECT
    id, unified_business_no, company_name, company_type, address, company_owner,
    contact_person, job_title, phone, mobile, email, backup_email, user_role,
    password_hash, status, remark, review_remark, created_at, updated_at, reviewed_at, reviewed_by
FROM registration_requests
WHERE id = $1
LIMIT 1
`

// 根據ID獲取單一註冊申請記錄
func (q *Queries) GetRegistrationRequestByID(ctx context.Context, id uint32) (*RegistrationRequest, error) {
	row := q.db.QueryRow(ctx, getRegistrationRequestByID, id)
	var i RegistrationRequest
	err := row.Scan(
		&i.ID,
		&i.UnifiedBusinessNo,
		&i.CompanyName,
		&i.CompanyType,
		&i.Address,
		&i.CompanyOwner,
		&i.ContactPerson,
		&i.JobTitle,
		&i.Phone,
		&i.Mobile,
		&i.Email,
		&i.BackupEmail,
		&i.UserRole,
		&i.PasswordHash,
		&i.Status,
		&i.Remark,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.ReviewedBy,
	)
	return &i, err
}

const getRegistrationRequestByUnifiedBusinessNo = `-- name: GetRegistrationRequestByUnifiedBusinessNo :one
SELECT
    id, unified_business_no, company_name, company_type, address, company_owner,
    contact_person, job_title, phone, mobile, email, backup_email, user_role,
    password_hash, status, remark, review_remark, created_at, updated_at, reviewed_at, reviewed_by
FROM registration_requests
WHERE unified_business_no = $1
LIMIT 1
`

// 根據統一編號查詢註冊申請，用於檢查是否有重複申請
func (q *Queries) GetRegistrationRequestByUnifiedBusinessNo(ctx context.Context, unifiedBusinessNo string) (*RegistrationRequest, error) {
	row := q.db.QueryRow(ctx, getRegistrationRequestByUnifiedBusinessNo, unifiedBusinessNo)
	var i RegistrationRequest
	err := row.Scan(
		&i.ID,
		&i.UnifiedBusinessNo,
		&i.CompanyName,
		&i.CompanyType,
		&i.Address,
		&i.CompanyOwner,
		&i.ContactPerson,
		&i.JobTitle,
		&i.Phone,
		&i.Mobile,
		&i.Email,
		&i.BackupEmail,
		&i.UserRole,
		&i.PasswordHash,
		&i.Status,
		&i.Remark,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.ReviewedBy,
	)
	return &i, err
}

const listRegistrationRequests = `-- name: ListRegistrationRequests :many
SELECT
    id, unified_business_no, company_name, company_type, address, company_owner,
    contact_person, job_title, phone, mobile, email, backup_email, user_role,
    password_hash, status, remark, review_remark, created_at, updated_at, reviewed_at, reviewed_by
FROM registration_requests
WHERE
    CASE WHEN $1::text != '' THEN status = $1::registration_requests_type ELSE TRUE END AND
    CASE WHEN $2::text != '' THEN
             company_name ILIKE '%' || $2 || '%' OR
             unified_business_no ILIKE '%' || $2 || '%' OR
             contact_person ILIKE '%' || $2 || '%' OR
             email ILIKE '%' || $2 || '%'
         ELSE TRUE END
ORDER BY
    CASE WHEN $3::text = 'created_at' AND $4::text = 'asc' THEN created_at END ASC,
    CASE WHEN $3::text = 'created_at' AND $4::text = 'desc' THEN created_at END DESC,
    CASE WHEN $3::text = 'company_name' AND $4::text = 'asc' THEN company_name END ASC,
    CASE WHEN $3::text = 'company_name' AND $4::text = 'desc' THEN company_name END DESC,
    created_at DESC
LIMIT CASE
          WHEN $6::int > 0 THEN $6::int
          WHEN $6 IS NULL THEN NULL
          ELSE 2147483647 -- PostgreSQL 的最大整數，實際上相當於無限制
    END
    OFFSET $5::int
`

type ListRegistrationRequestsParams struct {
	Status     string `json:"status"`
	SearchTerm string `json:"searchTerm"`
	SortBy     string `json:"sortBy"`
	SortDir    string `json:"sortDir"`
	OffsetVal  int32  `json:"offsetVal"`
	LimitVal   int32  `json:"limitVal"`
}

// 列出符合條件的註冊申請，支援分頁、搜尋和排序
func (q *Queries) ListRegistrationRequests(ctx context.Context, arg ListRegistrationRequestsParams) ([]*RegistrationRequest, error) {
	rows, err := q.db.Query(ctx, listRegistrationRequests,
		arg.Status,
		arg.SearchTerm,
		arg.SortBy,
		arg.SortDir,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*RegistrationRequest{}
	for rows.Next() {
		var i RegistrationRequest
		if err := rows.Scan(
			&i.ID,
			&i.UnifiedBusinessNo,
			&i.CompanyName,
			&i.CompanyType,
			&i.Address,
			&i.CompanyOwner,
			&i.ContactPerson,
			&i.JobTitle,
			&i.Phone,
			&i.Mobile,
			&i.Email,
			&i.BackupEmail,
			&i.UserRole,
			&i.PasswordHash,
			&i.Status,
			&i.Remark,
			&i.ReviewRemark,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ReviewedAt,
			&i.ReviewedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateRegistrationRequest = `-- name: UpdateRegistrationRequest :one
UPDATE registration_requests
SET
    company_name = COALESCE($2, company_name),
    company_type = COALESCE($3::company_type, company_type),
    address = COALESCE($4, address),
    company_owner = COALESCE($5, company_owner),
    contact_person = COALESCE($6, contact_person),
    job_title = COALESCE($7, job_title),
    phone = COALESCE($8, phone),
    mobile = COALESCE($9, mobile),
    email = COALESCE($10, email),
    backup_email = COALESCE($11, backup_email),
    password_hash = COALESCE($12, password_hash),
    remark = COALESCE($13, remark),
    status = CASE
                 WHEN $14::boolean = TRUE AND status = '註冊退件'
                     THEN '註冊待審'::registration_requests_type
                 ELSE status
        END,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, unified_business_no, company_name, company_type, address, company_owner,
    contact_person, job_title, phone, mobile, email, backup_email, user_role,
    password_hash, status, remark, review_remark, created_at, updated_at, reviewed_at, reviewed_by
`

type UpdateRegistrationRequestParams struct {
	ID            uint32          `json:"id"`
	CompanyName   *string         `json:"companyName"`
	CompanyType   NullCompanyType `json:"companyType"`
	Address       *string         `json:"address"`
	CompanyOwner  *string         `json:"companyOwner"`
	ContactPerson *string         `json:"contactPerson"`
	JobTitle      *string         `json:"jobTitle"`
	Phone         *string         `json:"phone"`
	Mobile        *string         `json:"mobile"`
	Email         *string         `json:"email"`
	BackupEmail   *string         `json:"backupEmail"`
	PasswordHash  *string         `json:"passwordHash"`
	Remark        *string         `json:"remark"`
	Resubmit      *bool           `json:"resubmit"`
}

// 更新註冊申請資料（用於退件後重新提交）
func (q *Queries) UpdateRegistrationRequest(ctx context.Context, arg UpdateRegistrationRequestParams) (*RegistrationRequest, error) {
	row := q.db.QueryRow(ctx, updateRegistrationRequest,
		arg.ID,
		arg.CompanyName,
		arg.CompanyType,
		arg.Address,
		arg.CompanyOwner,
		arg.ContactPerson,
		arg.JobTitle,
		arg.Phone,
		arg.Mobile,
		arg.Email,
		arg.BackupEmail,
		arg.PasswordHash,
		arg.Remark,
		arg.Resubmit,
	)
	var i RegistrationRequest
	err := row.Scan(
		&i.ID,
		&i.UnifiedBusinessNo,
		&i.CompanyName,
		&i.CompanyType,
		&i.Address,
		&i.CompanyOwner,
		&i.ContactPerson,
		&i.JobTitle,
		&i.Phone,
		&i.Mobile,
		&i.Email,
		&i.BackupEmail,
		&i.UserRole,
		&i.PasswordHash,
		&i.Status,
		&i.Remark,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.ReviewedBy,
	)
	return &i, err
}

const updateRegistrationRequestStatus = `-- name: UpdateRegistrationRequestStatus :one
UPDATE registration_requests
SET
    status = $2::registration_requests_type,
    review_remark = $3,
    reviewed_at = CURRENT_TIMESTAMP,
    reviewed_by = $4,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, unified_business_no, company_name, company_type, address, company_owner,
    contact_person, job_title, phone, mobile, email, backup_email, user_role,
    password_hash, status, remark, review_remark, created_at, updated_at, reviewed_at, reviewed_by
`

type UpdateRegistrationRequestStatusParams struct {
	ID           uint32                   `json:"id"`
	Column2      RegistrationRequestsType `json:"column2"`
	ReviewRemark *string                  `json:"reviewRemark"`
	ReviewedBy   uint32                   `json:"reviewedBy"`
}

// 更新註冊申請狀態（審核通過或退件）
func (q *Queries) UpdateRegistrationRequestStatus(ctx context.Context, arg UpdateRegistrationRequestStatusParams) (*RegistrationRequest, error) {
	row := q.db.QueryRow(ctx, updateRegistrationRequestStatus,
		arg.ID,
		arg.Column2,
		arg.ReviewRemark,
		arg.ReviewedBy,
	)
	var i RegistrationRequest
	err := row.Scan(
		&i.ID,
		&i.UnifiedBusinessNo,
		&i.CompanyName,
		&i.CompanyType,
		&i.Address,
		&i.CompanyOwner,
		&i.ContactPerson,
		&i.JobTitle,
		&i.Phone,
		&i.Mobile,
		&i.Email,
		&i.BackupEmail,
		&i.UserRole,
		&i.PasswordHash,
		&i.Status,
		&i.Remark,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.ReviewedBy,
	)
	return &i, err
}
