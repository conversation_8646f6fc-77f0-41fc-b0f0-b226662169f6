// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: reminder_schedule.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createReminderSchedule = `-- name: CreateReminderSchedule :one
INSERT INTO reminder_schedules (
    project_id,
    start_date,
    target_type,
    email_subject,
    email_content,
    status,
    expected_count,
    actual_count,
    created_by,
    updated_by
) VALUES (
             $1, $2, $3, $4, $5, $6, $7, 0, $8, $9
         )
RETURNING
    id, project_id, start_date, target_type, email_subject,
    email_content, status, last_sent_at, expected_count, actual_count,
    created_at, updated_at, created_by, updated_by
`

type CreateReminderScheduleParams struct {
	ProjectID     uint32           `json:"projectId"`
	StartDate     pgtype.Timestamp `json:"startDate"`
	TargetType    ReminderTarget   `json:"targetType"`
	EmailSubject  string           `json:"emailSubject"`
	EmailContent  string           `json:"emailContent"`
	Status        ReminderStatus   `json:"status"`
	ExpectedCount uint32           `json:"expectedCount"`
	CreatedBy     uint32           `json:"createdBy"`
	UpdatedBy     uint32           `json:"updatedBy"`
}

func (q *Queries) CreateReminderSchedule(ctx context.Context, arg CreateReminderScheduleParams) (*ReminderSchedule, error) {
	row := q.db.QueryRow(ctx, createReminderSchedule,
		arg.ProjectID,
		arg.StartDate,
		arg.TargetType,
		arg.EmailSubject,
		arg.EmailContent,
		arg.Status,
		arg.ExpectedCount,
		arg.CreatedBy,
		arg.UpdatedBy,
	)
	var i ReminderSchedule
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.StartDate,
		&i.TargetType,
		&i.EmailSubject,
		&i.EmailContent,
		&i.Status,
		&i.LastSentAt,
		&i.ExpectedCount,
		&i.ActualCount,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const deleteReminderSchedule = `-- name: DeleteReminderSchedule :exec
DELETE FROM reminder_schedules
WHERE id = $1
`

func (q *Queries) DeleteReminderSchedule(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteReminderSchedule, id)
	return err
}

const getReminderScheduleByID = `-- name: GetReminderScheduleByID :one
SELECT
    id,
    project_id,
    start_date,
    target_type,
    email_subject,
    email_content,
    status,
    last_sent_at,
    expected_count,
    actual_count,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM reminder_schedules
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetReminderScheduleByID(ctx context.Context, id uint32) (*ReminderSchedule, error) {
	row := q.db.QueryRow(ctx, getReminderScheduleByID, id)
	var i ReminderSchedule
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.StartDate,
		&i.TargetType,
		&i.EmailSubject,
		&i.EmailContent,
		&i.Status,
		&i.LastSentAt,
		&i.ExpectedCount,
		&i.ActualCount,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const listPendingReminderSchedules = `-- name: ListPendingReminderSchedules :many
SELECT
    id,
    project_id,
    start_date,
    target_type,
    email_subject,
    email_content,
    status,
    last_sent_at,
    expected_count,
    actual_count,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM reminder_schedules
WHERE
    status = '未開始' AND
    start_date <= CURRENT_TIMESTAMP
ORDER BY start_date ASC
`

func (q *Queries) ListPendingReminderSchedules(ctx context.Context) ([]*ReminderSchedule, error) {
	rows, err := q.db.Query(ctx, listPendingReminderSchedules)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ReminderSchedule{}
	for rows.Next() {
		var i ReminderSchedule
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.StartDate,
			&i.TargetType,
			&i.EmailSubject,
			&i.EmailContent,
			&i.Status,
			&i.LastSentAt,
			&i.ExpectedCount,
			&i.ActualCount,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CreatedBy,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listReminderSchedulesByProjectID = `-- name: ListReminderSchedulesByProjectID :many
SELECT
    id,
    project_id,
    start_date,
    target_type,
    email_subject,
    email_content,
    status,
    last_sent_at,
    expected_count,
    actual_count,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM reminder_schedules
WHERE project_id = $1
ORDER BY start_date DESC
`

func (q *Queries) ListReminderSchedulesByProjectID(ctx context.Context, projectID uint32) ([]*ReminderSchedule, error) {
	rows, err := q.db.Query(ctx, listReminderSchedulesByProjectID, projectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ReminderSchedule{}
	for rows.Next() {
		var i ReminderSchedule
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.StartDate,
			&i.TargetType,
			&i.EmailSubject,
			&i.EmailContent,
			&i.Status,
			&i.LastSentAt,
			&i.ExpectedCount,
			&i.ActualCount,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CreatedBy,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateReminderSchedule = `-- name: UpdateReminderSchedule :one
UPDATE reminder_schedules
SET
    start_date = COALESCE($2, start_date),
    target_type = COALESCE($3::reminder_target, target_type),
    email_subject = COALESCE($4, email_subject),
    email_content = COALESCE($5, email_content),
    status = COALESCE($6::reminder_status, status),
    expected_count = COALESCE($7, expected_count),
    updated_at = CURRENT_TIMESTAMP,
    updated_by = COALESCE($8, updated_by)
WHERE id = $1
RETURNING
    id, project_id, start_date, target_type, email_subject,
    email_content, status, last_sent_at, expected_count, actual_count,
    created_at, updated_at, created_by, updated_by
`

type UpdateReminderScheduleParams struct {
	ID            uint32             `json:"id"`
	StartDate     pgtype.Timestamp   `json:"startDate"`
	TargetType    NullReminderTarget `json:"targetType"`
	EmailSubject  *string            `json:"emailSubject"`
	EmailContent  *string            `json:"emailContent"`
	Status        NullReminderStatus `json:"status"`
	ExpectedCount uint32             `json:"expectedCount"`
	UpdatedBy     uint32             `json:"updatedBy"`
}

func (q *Queries) UpdateReminderSchedule(ctx context.Context, arg UpdateReminderScheduleParams) (*ReminderSchedule, error) {
	row := q.db.QueryRow(ctx, updateReminderSchedule,
		arg.ID,
		arg.StartDate,
		arg.TargetType,
		arg.EmailSubject,
		arg.EmailContent,
		arg.Status,
		arg.ExpectedCount,
		arg.UpdatedBy,
	)
	var i ReminderSchedule
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.StartDate,
		&i.TargetType,
		&i.EmailSubject,
		&i.EmailContent,
		&i.Status,
		&i.LastSentAt,
		&i.ExpectedCount,
		&i.ActualCount,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const updateReminderScheduleStatus = `-- name: UpdateReminderScheduleStatus :one
UPDATE reminder_schedules
SET
    status = $2::reminder_status,
    actual_count = $3,
    last_sent_at = CURRENT_TIMESTAMP,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING
    id, project_id, start_date, target_type, email_subject,
    email_content, status, last_sent_at, expected_count, actual_count,
    created_at, updated_at, created_by, updated_by
`

type UpdateReminderScheduleStatusParams struct {
	ID          uint32         `json:"id"`
	Column2     ReminderStatus `json:"column2"`
	ActualCount uint32         `json:"actualCount"`
}

func (q *Queries) UpdateReminderScheduleStatus(ctx context.Context, arg UpdateReminderScheduleStatusParams) (*ReminderSchedule, error) {
	row := q.db.QueryRow(ctx, updateReminderScheduleStatus, arg.ID, arg.Column2, arg.ActualCount)
	var i ReminderSchedule
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.StartDate,
		&i.TargetType,
		&i.EmailSubject,
		&i.EmailContent,
		&i.Status,
		&i.LastSentAt,
		&i.ExpectedCount,
		&i.ActualCount,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}
