// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: project.sql

package sqlc

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const countActiveProjects = `-- name: CountActiveProjects :one
SELECT COUNT(id)
FROM projects
WHERE status = '進行中' AND is_delete = false
`

func (q *Queries) CountActiveProjects(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, countActiveProjects)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countProjects = `-- name: CountProjects :one
SELECT COUNT(id)
FROM projects
WHERE
    is_delete = false AND
    CASE WHEN $1::text != '' THEN type = $1::project_type ELSE TRUE END AND
    CASE WHEN $2::text != '' THEN category = $2::project_category ELSE TRUE END AND
    CASE WHEN $3::text != '' THEN status = $3::project_status ELSE TRUE END AND
    CASE WHEN $4::boolean IS NOT NULL THEN is_test = $4 ELSE TRUE END AND
    CASE WHEN $5::text != '' THEN
             name ILIKE '%' || $5 || '%'
         ELSE TRUE END
`

type CountProjectsParams struct {
	Type       string `json:"type"`
	Category   string `json:"category"`
	Status     string `json:"status"`
	IsTest     bool   `json:"isTest"`
	SearchTerm string `json:"searchTerm"`
}

func (q *Queries) CountProjects(ctx context.Context, arg CountProjectsParams) (int64, error) {
	row := q.db.QueryRow(ctx, countProjects,
		arg.Type,
		arg.Category,
		arg.Status,
		arg.IsTest,
		arg.SearchTerm,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createProject = `-- name: CreateProject :one
INSERT INTO projects (
    name,
    type,
    category,
    status,
    cisa_fill_time_start,
    cisa_fill_time_end,
    company_fill_time_start,
    company_fill_time_end,
    company_correction_start,
    company_correction_end,
    attachment_space,
    roc_price_reference_year,
    remarks,
    is_test,
    created_by,
    updated_by
) VALUES (
             $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
         )
RETURNING
    id, name, type, category, status,
    cisa_fill_time_start, cisa_fill_time_end,
    company_fill_time_start, company_fill_time_end,
    company_correction_start, company_correction_end,
    attachment_space, roc_price_reference_year, remarks,is_test, created_at, updated_at, created_by, updated_by
`

type CreateProjectParams struct {
	Name                   string           `json:"name"`
	Type                   ProjectType      `json:"type"`
	Category               ProjectCategory  `json:"category"`
	Status                 ProjectStatus    `json:"status"`
	CisaFillTimeStart      pgtype.Timestamp `json:"cisaFillTimeStart"`
	CisaFillTimeEnd        pgtype.Timestamp `json:"cisaFillTimeEnd"`
	CompanyFillTimeStart   pgtype.Timestamp `json:"companyFillTimeStart"`
	CompanyFillTimeEnd     pgtype.Timestamp `json:"companyFillTimeEnd"`
	CompanyCorrectionStart pgtype.Timestamp `json:"companyCorrectionStart"`
	CompanyCorrectionEnd   pgtype.Timestamp `json:"companyCorrectionEnd"`
	AttachmentSpace        *int64           `json:"attachmentSpace"`
	RocPriceReferenceYear  *int32           `json:"rocPriceReferenceYear"`
	Remarks                *string          `json:"remarks"`
	IsTest                 bool             `json:"isTest"`
	CreatedBy              uint32           `json:"createdBy"`
	UpdatedBy              uint32           `json:"updatedBy"`
}

type CreateProjectRow struct {
	ID                     uint32           `json:"id"`
	Name                   string           `json:"name"`
	Type                   ProjectType      `json:"type"`
	Category               ProjectCategory  `json:"category"`
	Status                 ProjectStatus    `json:"status"`
	CisaFillTimeStart      pgtype.Timestamp `json:"cisaFillTimeStart"`
	CisaFillTimeEnd        pgtype.Timestamp `json:"cisaFillTimeEnd"`
	CompanyFillTimeStart   pgtype.Timestamp `json:"companyFillTimeStart"`
	CompanyFillTimeEnd     pgtype.Timestamp `json:"companyFillTimeEnd"`
	CompanyCorrectionStart pgtype.Timestamp `json:"companyCorrectionStart"`
	CompanyCorrectionEnd   pgtype.Timestamp `json:"companyCorrectionEnd"`
	AttachmentSpace        *int64           `json:"attachmentSpace"`
	RocPriceReferenceYear  *int32           `json:"rocPriceReferenceYear"`
	Remarks                *string          `json:"remarks"`
	IsTest                 bool             `json:"isTest"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
	CreatedBy              uint32           `json:"createdBy"`
	UpdatedBy              uint32           `json:"updatedBy"`
}

func (q *Queries) CreateProject(ctx context.Context, arg CreateProjectParams) (*CreateProjectRow, error) {
	row := q.db.QueryRow(ctx, createProject,
		arg.Name,
		arg.Type,
		arg.Category,
		arg.Status,
		arg.CisaFillTimeStart,
		arg.CisaFillTimeEnd,
		arg.CompanyFillTimeStart,
		arg.CompanyFillTimeEnd,
		arg.CompanyCorrectionStart,
		arg.CompanyCorrectionEnd,
		arg.AttachmentSpace,
		arg.RocPriceReferenceYear,
		arg.Remarks,
		arg.IsTest,
		arg.CreatedBy,
		arg.UpdatedBy,
	)
	var i CreateProjectRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Type,
		&i.Category,
		&i.Status,
		&i.CisaFillTimeStart,
		&i.CisaFillTimeEnd,
		&i.CompanyFillTimeStart,
		&i.CompanyFillTimeEnd,
		&i.CompanyCorrectionStart,
		&i.CompanyCorrectionEnd,
		&i.AttachmentSpace,
		&i.RocPriceReferenceYear,
		&i.Remarks,
		&i.IsTest,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const deleteProject = `-- name: DeleteProject :exec
UPDATE projects
SET
    is_delete = true,
    updated_at = CURRENT_TIMESTAMP,
    updated_by = $2
WHERE id = $1
`

type DeleteProjectParams struct {
	ID        uint32 `json:"id"`
	UpdatedBy uint32 `json:"updatedBy"`
}

func (q *Queries) DeleteProject(ctx context.Context, arg DeleteProjectParams) error {
	_, err := q.db.Exec(ctx, deleteProject, arg.ID, arg.UpdatedBy)
	return err
}

const getProjectByID = `-- name: GetProjectByID :one
SELECT
    id,
    name,
    type,
    category,
    status,
    cisa_fill_time_start,
    cisa_fill_time_end,
    company_fill_time_start,
    company_fill_time_end,
    company_correction_start,
    company_correction_end,
    attachment_space,
    roc_price_reference_year,
    remarks,
    is_delete,
    is_test,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM projects
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetProjectByID(ctx context.Context, id uint32) (*Project, error) {
	row := q.db.QueryRow(ctx, getProjectByID, id)
	var i Project
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Type,
		&i.Category,
		&i.Status,
		&i.CisaFillTimeStart,
		&i.CisaFillTimeEnd,
		&i.CompanyFillTimeStart,
		&i.CompanyFillTimeEnd,
		&i.CompanyCorrectionStart,
		&i.CompanyCorrectionEnd,
		&i.AttachmentSpace,
		&i.RocPriceReferenceYear,
		&i.Remarks,
		&i.IsDelete,
		&i.IsTest,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const getProjectByIDWithCreator = `-- name: GetProjectByIDWithCreator :one
SELECT
    p.id,
    p.name,
    p.type,
    p.category,
    p.status,
    p.cisa_fill_time_start,
    p.cisa_fill_time_end,
    p.company_fill_time_start,
    p.company_fill_time_end,
    p.company_correction_start,
    p.company_correction_end,
    p.attachment_space,
    p.roc_price_reference_year,
    p.remarks,
    p.is_test,
    p.created_at,
    p.updated_at,
    p.created_by,
    p.updated_by,
    u.username as creator_name
FROM projects p
         LEFT JOIN users u ON p.created_by = u.id
WHERE p.id = $1
LIMIT 1
`

type GetProjectByIDWithCreatorRow struct {
	ID                     uint32           `json:"id"`
	Name                   string           `json:"name"`
	Type                   ProjectType      `json:"type"`
	Category               ProjectCategory  `json:"category"`
	Status                 ProjectStatus    `json:"status"`
	CisaFillTimeStart      pgtype.Timestamp `json:"cisaFillTimeStart"`
	CisaFillTimeEnd        pgtype.Timestamp `json:"cisaFillTimeEnd"`
	CompanyFillTimeStart   pgtype.Timestamp `json:"companyFillTimeStart"`
	CompanyFillTimeEnd     pgtype.Timestamp `json:"companyFillTimeEnd"`
	CompanyCorrectionStart pgtype.Timestamp `json:"companyCorrectionStart"`
	CompanyCorrectionEnd   pgtype.Timestamp `json:"companyCorrectionEnd"`
	AttachmentSpace        *int64           `json:"attachmentSpace"`
	RocPriceReferenceYear  *int32           `json:"rocPriceReferenceYear"`
	Remarks                *string          `json:"remarks"`
	IsTest                 bool             `json:"isTest"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
	CreatedBy              uint32           `json:"createdBy"`
	UpdatedBy              uint32           `json:"updatedBy"`
	CreatorName            *string          `json:"creatorName"`
}

func (q *Queries) GetProjectByIDWithCreator(ctx context.Context, id uint32) (*GetProjectByIDWithCreatorRow, error) {
	row := q.db.QueryRow(ctx, getProjectByIDWithCreator, id)
	var i GetProjectByIDWithCreatorRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Type,
		&i.Category,
		&i.Status,
		&i.CisaFillTimeStart,
		&i.CisaFillTimeEnd,
		&i.CompanyFillTimeStart,
		&i.CompanyFillTimeEnd,
		&i.CompanyCorrectionStart,
		&i.CompanyCorrectionEnd,
		&i.AttachmentSpace,
		&i.RocPriceReferenceYear,
		&i.Remarks,
		&i.IsTest,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
		&i.CreatorName,
	)
	return &i, err
}

const getProjectByName = `-- name: GetProjectByName :one
SELECT
    id,
    name,
    type,
    category,
    status,
    cisa_fill_time_start,
    cisa_fill_time_end,
    company_fill_time_start,
    company_fill_time_end,
    company_correction_start,
    company_correction_end,
    attachment_space,
    roc_price_reference_year,
    remarks,
    is_delete,
    is_test,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM projects
WHERE name = $1
LIMIT 1
`

func (q *Queries) GetProjectByName(ctx context.Context, name string) (*Project, error) {
	row := q.db.QueryRow(ctx, getProjectByName, name)
	var i Project
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Type,
		&i.Category,
		&i.Status,
		&i.CisaFillTimeStart,
		&i.CisaFillTimeEnd,
		&i.CompanyFillTimeStart,
		&i.CompanyFillTimeEnd,
		&i.CompanyCorrectionStart,
		&i.CompanyCorrectionEnd,
		&i.AttachmentSpace,
		&i.RocPriceReferenceYear,
		&i.Remarks,
		&i.IsDelete,
		&i.IsTest,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const isUserProjectMember = `-- name: IsUserProjectMember :one
SELECT EXISTS (
    SELECT 1
    FROM quotes q
    WHERE q.project_id = $2 AND q.user_id = $1 AND q.is_deleted = false AND q.quote_type = '廠商報價'
)
`

type IsUserProjectMemberParams struct {
	UserID    uint32 `json:"userId"`
	ProjectID uint32 `json:"projectId"`
}

func (q *Queries) IsUserProjectMember(ctx context.Context, arg IsUserProjectMemberParams) (bool, error) {
	row := q.db.QueryRow(ctx, isUserProjectMember, arg.UserID, arg.ProjectID)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const listActiveProjects = `-- name: ListActiveProjects :many
SELECT
    id,
    name,
    type,
    category,
    status,
    cisa_fill_time_start,
    cisa_fill_time_end,
    company_fill_time_start,
    company_fill_time_end,
    company_correction_start,
    company_correction_end,
    attachment_space,
    roc_price_reference_year,
    remarks,
    is_delete,
    is_test,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM projects
WHERE status = '進行中' AND is_delete = false
ORDER BY created_at DESC
`

func (q *Queries) ListActiveProjects(ctx context.Context) ([]*Project, error) {
	rows, err := q.db.Query(ctx, listActiveProjects)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Project{}
	for rows.Next() {
		var i Project
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Type,
			&i.Category,
			&i.Status,
			&i.CisaFillTimeStart,
			&i.CisaFillTimeEnd,
			&i.CompanyFillTimeStart,
			&i.CompanyFillTimeEnd,
			&i.CompanyCorrectionStart,
			&i.CompanyCorrectionEnd,
			&i.AttachmentSpace,
			&i.RocPriceReferenceYear,
			&i.Remarks,
			&i.IsDelete,
			&i.IsTest,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CreatedBy,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listProjects = `-- name: ListProjects :many
SELECT
    id,
    name,
    type,
    category,
    status,
    cisa_fill_time_start,
    cisa_fill_time_end,
    company_fill_time_start,
    company_fill_time_end,
    company_correction_start,
    company_correction_end,
    attachment_space,
    roc_price_reference_year,
    remarks,
    is_delete,
    is_test,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM projects
WHERE
    is_delete = false AND
    CASE WHEN $1::text != '' THEN type = $1::project_type ELSE TRUE END AND
    CASE WHEN $2::text != '' THEN category = $2::project_category ELSE TRUE END AND
    CASE WHEN $3::text != '' THEN status = $3::project_status ELSE TRUE END AND
    CASE WHEN $4::boolean IS NOT NULL THEN is_test = $4 ELSE FALSE END AND
    CASE WHEN $5::text != '' THEN
             name ILIKE '%' || $5 || '%'
         ELSE TRUE END
ORDER BY
    CASE
        WHEN $6::text = 'created_at' AND $7::text = 'asc'
            THEN to_char(created_at, 'YYYY-MM-DD HH24:MI:SS')
        END ASC,
    CASE
        WHEN $6::text = 'created_at' AND $7::text = 'desc'
            THEN to_char(created_at, 'YYYY-MM-DD HH24:MI:SS')
        END DESC,
    CASE
        WHEN $6::text = 'name' AND $7::text = 'asc'
            THEN name
        END ASC,
    CASE
        WHEN $6::text = 'name' AND $7::text = 'desc'
            THEN name
        END DESC,
    created_at DESC
LIMIT CASE
          WHEN $9::int > 0 THEN $9::int
          WHEN $9 IS NULL THEN NULL
          ELSE 2147483647 -- PostgreSQL 的最大整數，實際上相當於無限制
END
OFFSET $8::int
`

type ListProjectsParams struct {
	Type       string `json:"type"`
	Category   string `json:"category"`
	Status     string `json:"status"`
	IsTest     bool   `json:"isTest"`
	SearchTerm string `json:"searchTerm"`
	SortBy     string `json:"sortBy"`
	SortDir    string `json:"sortDir"`
	OffsetVal  int32  `json:"offsetVal"`
	LimitVal   int32  `json:"limitVal"`
}

func (q *Queries) ListProjects(ctx context.Context, arg ListProjectsParams) ([]*Project, error) {
	rows, err := q.db.Query(ctx, listProjects,
		arg.Type,
		arg.Category,
		arg.Status,
		arg.IsTest,
		arg.SearchTerm,
		arg.SortBy,
		arg.SortDir,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Project{}
	for rows.Next() {
		var i Project
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Type,
			&i.Category,
			&i.Status,
			&i.CisaFillTimeStart,
			&i.CisaFillTimeEnd,
			&i.CompanyFillTimeStart,
			&i.CompanyFillTimeEnd,
			&i.CompanyCorrectionStart,
			&i.CompanyCorrectionEnd,
			&i.AttachmentSpace,
			&i.RocPriceReferenceYear,
			&i.Remarks,
			&i.IsDelete,
			&i.IsTest,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CreatedBy,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateProject = `-- name: UpdateProject :one
UPDATE projects
SET
    name = COALESCE($2, name),
    type = COALESCE($3::project_type, type),
    category = COALESCE($4::project_category, category),
    status = COALESCE($5::project_status, status),
    cisa_fill_time_start = COALESCE($6, cisa_fill_time_start),
    cisa_fill_time_end = COALESCE($7, cisa_fill_time_end),
    company_fill_time_start = COALESCE($8, company_fill_time_start),
    company_fill_time_end = COALESCE($9, company_fill_time_end),
    company_correction_start = COALESCE($10, company_correction_start),
    company_correction_end = COALESCE($11, company_correction_end),
    attachment_space = COALESCE($12, attachment_space),
    roc_price_reference_year = COALESCE($13, roc_price_reference_year),
    remarks = COALESCE($14, remarks),
    is_test = COALESCE($15, is_test),
    updated_at = CURRENT_TIMESTAMP,
    updated_by = COALESCE($16, updated_by)
WHERE id = $1
RETURNING
    id, name, type, category, status,
    cisa_fill_time_start, cisa_fill_time_end,
    company_fill_time_start, company_fill_time_end,
    company_correction_start, company_correction_end,
    attachment_space, is_test, created_at, updated_at, created_by, updated_by
`

type UpdateProjectParams struct {
	ID                     uint32              `json:"id"`
	Name                   *string             `json:"name"`
	Type                   NullProjectType     `json:"type"`
	Category               NullProjectCategory `json:"category"`
	Status                 NullProjectStatus   `json:"status"`
	CisaFillTimeStart      pgtype.Timestamp    `json:"cisaFillTimeStart"`
	CisaFillTimeEnd        pgtype.Timestamp    `json:"cisaFillTimeEnd"`
	CompanyFillTimeStart   pgtype.Timestamp    `json:"companyFillTimeStart"`
	CompanyFillTimeEnd     pgtype.Timestamp    `json:"companyFillTimeEnd"`
	CompanyCorrectionStart pgtype.Timestamp    `json:"companyCorrectionStart"`
	CompanyCorrectionEnd   pgtype.Timestamp    `json:"companyCorrectionEnd"`
	AttachmentSpace        *int64              `json:"attachmentSpace"`
	RocPriceReferenceYear  *int32              `json:"rocPriceReferenceYear"`
	Remarks                *string             `json:"remarks"`
	IsTest                 *bool               `json:"isTest"`
	UpdatedBy              uint32              `json:"updatedBy"`
}

type UpdateProjectRow struct {
	ID                     uint32           `json:"id"`
	Name                   string           `json:"name"`
	Type                   ProjectType      `json:"type"`
	Category               ProjectCategory  `json:"category"`
	Status                 ProjectStatus    `json:"status"`
	CisaFillTimeStart      pgtype.Timestamp `json:"cisaFillTimeStart"`
	CisaFillTimeEnd        pgtype.Timestamp `json:"cisaFillTimeEnd"`
	CompanyFillTimeStart   pgtype.Timestamp `json:"companyFillTimeStart"`
	CompanyFillTimeEnd     pgtype.Timestamp `json:"companyFillTimeEnd"`
	CompanyCorrectionStart pgtype.Timestamp `json:"companyCorrectionStart"`
	CompanyCorrectionEnd   pgtype.Timestamp `json:"companyCorrectionEnd"`
	AttachmentSpace        *int64           `json:"attachmentSpace"`
	IsTest                 bool             `json:"isTest"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
	CreatedBy              uint32           `json:"createdBy"`
	UpdatedBy              uint32           `json:"updatedBy"`
}

func (q *Queries) UpdateProject(ctx context.Context, arg UpdateProjectParams) (*UpdateProjectRow, error) {
	row := q.db.QueryRow(ctx, updateProject,
		arg.ID,
		arg.Name,
		arg.Type,
		arg.Category,
		arg.Status,
		arg.CisaFillTimeStart,
		arg.CisaFillTimeEnd,
		arg.CompanyFillTimeStart,
		arg.CompanyFillTimeEnd,
		arg.CompanyCorrectionStart,
		arg.CompanyCorrectionEnd,
		arg.AttachmentSpace,
		arg.RocPriceReferenceYear,
		arg.Remarks,
		arg.IsTest,
		arg.UpdatedBy,
	)
	var i UpdateProjectRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Type,
		&i.Category,
		&i.Status,
		&i.CisaFillTimeStart,
		&i.CisaFillTimeEnd,
		&i.CompanyFillTimeStart,
		&i.CompanyFillTimeEnd,
		&i.CompanyCorrectionStart,
		&i.CompanyCorrectionEnd,
		&i.AttachmentSpace,
		&i.IsTest,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const updateProjectStatus = `-- name: UpdateProjectStatus :one
UPDATE projects
SET
    status = $2::project_status,
    updated_at = CURRENT_TIMESTAMP,
    updated_by = $3
WHERE id = $1
RETURNING
    id, name, type, category, status,
    cisa_fill_time_start, cisa_fill_time_end,
    company_fill_time_start, company_fill_time_end,
    company_correction_start, company_correction_end,
    attachment_space,roc_price_reference_year,
    remarks, is_test, created_at, updated_at, created_by, updated_by
`

type UpdateProjectStatusParams struct {
	ID        uint32        `json:"id"`
	Column2   ProjectStatus `json:"column2"`
	UpdatedBy uint32        `json:"updatedBy"`
}

type UpdateProjectStatusRow struct {
	ID                     uint32           `json:"id"`
	Name                   string           `json:"name"`
	Type                   ProjectType      `json:"type"`
	Category               ProjectCategory  `json:"category"`
	Status                 ProjectStatus    `json:"status"`
	CisaFillTimeStart      pgtype.Timestamp `json:"cisaFillTimeStart"`
	CisaFillTimeEnd        pgtype.Timestamp `json:"cisaFillTimeEnd"`
	CompanyFillTimeStart   pgtype.Timestamp `json:"companyFillTimeStart"`
	CompanyFillTimeEnd     pgtype.Timestamp `json:"companyFillTimeEnd"`
	CompanyCorrectionStart pgtype.Timestamp `json:"companyCorrectionStart"`
	CompanyCorrectionEnd   pgtype.Timestamp `json:"companyCorrectionEnd"`
	AttachmentSpace        *int64           `json:"attachmentSpace"`
	RocPriceReferenceYear  *int32           `json:"rocPriceReferenceYear"`
	Remarks                *string          `json:"remarks"`
	IsTest                 bool             `json:"isTest"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
	CreatedBy              uint32           `json:"createdBy"`
	UpdatedBy              uint32           `json:"updatedBy"`
}

func (q *Queries) UpdateProjectStatus(ctx context.Context, arg UpdateProjectStatusParams) (*UpdateProjectStatusRow, error) {
	row := q.db.QueryRow(ctx, updateProjectStatus, arg.ID, arg.Column2, arg.UpdatedBy)
	var i UpdateProjectStatusRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Type,
		&i.Category,
		&i.Status,
		&i.CisaFillTimeStart,
		&i.CisaFillTimeEnd,
		&i.CompanyFillTimeStart,
		&i.CompanyFillTimeEnd,
		&i.CompanyCorrectionStart,
		&i.CompanyCorrectionEnd,
		&i.AttachmentSpace,
		&i.RocPriceReferenceYear,
		&i.Remarks,
		&i.IsTest,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}
