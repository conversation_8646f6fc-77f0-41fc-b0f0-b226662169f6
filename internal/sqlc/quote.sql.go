// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: quote.sql

package sqlc

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type BatchCreateQuotesParams struct {
	ProjectID      uint32         `json:"projectId"`
	ProductID      uint32         `json:"productId"`
	UserID         uint32         `json:"userId"`
	QuoteType      QuoteType      `json:"quoteType"`
	MarketPrice    pgtype.Numeric `json:"marketPrice"`
	InternetPrice  pgtype.Numeric `json:"internetPrice"`
	OriginalPrice  pgtype.Numeric `json:"originalPrice"`
	PromotionPrice pgtype.Numeric `json:"promotionPrice"`
	BidPrice       pgtype.Numeric `json:"bidPrice"`
	SameAsBidPrice *bool          `json:"sameAsBidPrice"`
	Status         QuoteStatus    `json:"status"`
	Remark         *string        `json:"remark"`
	IsDeleted      bool           `json:"isDeleted"`
}

const batchUpdateQuoteStatus = `-- name: BatchUpdateQuoteStatus :many
WITH updated_quotes AS (
    UPDATE quotes
        SET
            status = $2::quote_status,
            admin_remark = $3,
            reviewed_by = $4,
            batch_id = $5,
            reviewed_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE
            id = ANY($1::bigint[]) AND
            is_deleted = FALSE
        RETURNING
            id, project_id, product_id, user_id, quote_type,
            market_price, internet_price, original_price, promotion_price, bid_price,
            same_as_bid_price, status, remark, admin_remark, is_deleted,
            created_at, updated_at, reviewed_at, reviewed_by, batch_id
)
INSERT INTO quote_approval_history (
    quote_id,
    old_status,
    new_status,
    remark,
    created_by,
    batch_id
)
SELECT
    id,
    '待審'::quote_status,
    $2::quote_status,
    $3,
    $4,
    $5
FROM updated_quotes
RETURNING quote_id
`

type BatchUpdateQuoteStatusParams struct {
	Column1   []int64     `json:"column1"`
	Column2   QuoteStatus `json:"column2"`
	Remark    *string     `json:"remark"`
	CreatedBy uint32      `json:"createdBy"`
	BatchID   uint32      `json:"batchId"`
}

func (q *Queries) BatchUpdateQuoteStatus(ctx context.Context, arg BatchUpdateQuoteStatusParams) ([]uint32, error) {
	rows, err := q.db.Query(ctx, batchUpdateQuoteStatus,
		arg.Column1,
		arg.Column2,
		arg.Remark,
		arg.CreatedBy,
		arg.BatchID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []uint32{}
	for rows.Next() {
		var quote_id uint32
		if err := rows.Scan(&quote_id); err != nil {
			return nil, err
		}
		items = append(items, quote_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const countPendingQuotesByCompany = `-- name: CountPendingQuotesByCompany :one
SELECT COUNT(q.id)
FROM quotes q
         JOIN users u ON q.user_id = u.id
WHERE
    q.is_deleted = FALSE AND
    q.status = '待審' AND
    u.company_id = $1
`

func (q *Queries) CountPendingQuotesByCompany(ctx context.Context, companyID uint32) (int64, error) {
	row := q.db.QueryRow(ctx, countPendingQuotesByCompany, companyID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countQuotes = `-- name: CountQuotes :one
SELECT COUNT(q.id)
FROM quotes q
         JOIN products p ON q.product_id = p.id
         JOIN product_groups pg ON p.group_id = pg.id
         JOIN users u ON q.user_id = u.id
         LEFT JOIN companies c ON u.company_id = c.id
WHERE
    q.is_deleted = FALSE AND
    CASE WHEN $1::bigint > 0 THEN q.project_id = $1 ELSE TRUE END AND
    CASE WHEN $2::bigint > 0 THEN q.product_id = $2 ELSE TRUE END AND
    CASE WHEN $3::bigint > 0 THEN q.user_id = $3 ELSE TRUE END AND
    CASE WHEN $4::bigint > 0 THEN c.id = $4 ELSE TRUE END AND
    CASE WHEN $5::text != '' THEN q.quote_type = $5::quote_type ELSE TRUE END AND
    CASE WHEN $6::text != '' THEN q.status = $6::quote_status ELSE TRUE END AND
    CASE WHEN $7::bigint > 0 THEN p.group_id = $7 ELSE TRUE END AND
    CASE WHEN $8::text != '' THEN
             p.name ILIKE '%' || $8 || '%' OR
             u.username ILIKE '%' || $8 || '%' OR
             COALESCE(c.company_name, '') ILIKE '%' || $8 || '%'
         ELSE TRUE END
`

type CountQuotesParams struct {
	ProjectID  *int64  `json:"projectId"`
	ProductID  *int64  `json:"productId"`
	UserID     *int64  `json:"userId"`
	CompanyID  *int64  `json:"companyId"`
	QuoteType  *string `json:"quoteType"`
	Status     *string `json:"status"`
	GroupID    *int64  `json:"groupId"`
	SearchTerm *string `json:"searchTerm"`
}

func (q *Queries) CountQuotes(ctx context.Context, arg CountQuotesParams) (*int64, error) {
	row := q.db.QueryRow(ctx, countQuotes,
		arg.ProjectID,
		arg.ProductID,
		arg.UserID,
		arg.CompanyID,
		arg.QuoteType,
		arg.Status,
		arg.GroupID,
		arg.SearchTerm,
	)
	var count *int64
	err := row.Scan(&count)
	return count, err
}

const createQuote = `-- name: CreateQuote :one
INSERT INTO quotes (
    project_id,
    product_id,
    user_id,
    quote_type,
    market_price,
    internet_price,
    original_price,
    promotion_price,
    bid_price,
    same_as_bid_price,
    status,
    remark,
    is_deleted
) VALUES (
             $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, FALSE
         )
RETURNING
    id, project_id, product_id, user_id, quote_type,
    market_price, internet_price, original_price, promotion_price, bid_price,
    same_as_bid_price, status, remark, admin_remark, is_deleted,
    created_at, updated_at, reviewed_at, reviewed_by, batch_id
`

type CreateQuoteParams struct {
	ProjectID      uint32         `json:"projectId"`
	ProductID      uint32         `json:"productId"`
	UserID         uint32         `json:"userId"`
	QuoteType      QuoteType      `json:"quoteType"`
	MarketPrice    pgtype.Numeric `json:"marketPrice"`
	InternetPrice  pgtype.Numeric `json:"internetPrice"`
	OriginalPrice  pgtype.Numeric `json:"originalPrice"`
	PromotionPrice pgtype.Numeric `json:"promotionPrice"`
	BidPrice       pgtype.Numeric `json:"bidPrice"`
	SameAsBidPrice *bool          `json:"sameAsBidPrice"`
	Status         QuoteStatus    `json:"status"`
	Remark         *string        `json:"remark"`
}

func (q *Queries) CreateQuote(ctx context.Context, arg CreateQuoteParams) (*Quote, error) {
	row := q.db.QueryRow(ctx, createQuote,
		arg.ProjectID,
		arg.ProductID,
		arg.UserID,
		arg.QuoteType,
		arg.MarketPrice,
		arg.InternetPrice,
		arg.OriginalPrice,
		arg.PromotionPrice,
		arg.BidPrice,
		arg.SameAsBidPrice,
		arg.Status,
		arg.Remark,
	)
	var i Quote
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.ProductID,
		&i.UserID,
		&i.QuoteType,
		&i.MarketPrice,
		&i.InternetPrice,
		&i.OriginalPrice,
		&i.PromotionPrice,
		&i.BidPrice,
		&i.SameAsBidPrice,
		&i.Status,
		&i.Remark,
		&i.AdminRemark,
		&i.IsDeleted,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.ReviewedBy,
		&i.BatchID,
	)
	return &i, err
}

const deleteQuote = `-- name: DeleteQuote :one
UPDATE quotes
SET
    is_deleted = TRUE,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING
    id, project_id, product_id, user_id, quote_type,
    market_price, internet_price, original_price, promotion_price, bid_price,
    same_as_bid_price, status, remark, admin_remark, is_deleted,
    created_at, updated_at, reviewed_at, reviewed_by, batch_id
`

func (q *Queries) DeleteQuote(ctx context.Context, id uint32) (*Quote, error) {
	row := q.db.QueryRow(ctx, deleteQuote, id)
	var i Quote
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.ProductID,
		&i.UserID,
		&i.QuoteType,
		&i.MarketPrice,
		&i.InternetPrice,
		&i.OriginalPrice,
		&i.PromotionPrice,
		&i.BidPrice,
		&i.SameAsBidPrice,
		&i.Status,
		&i.Remark,
		&i.AdminRemark,
		&i.IsDeleted,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.ReviewedBy,
		&i.BatchID,
	)
	return &i, err
}

const getQuoteByID = `-- name: GetQuoteByID :one
SELECT
    id,
    project_id,
    product_id,
    user_id,
    quote_type,
    market_price,
    internet_price,
    original_price,
    promotion_price,
    bid_price,
    same_as_bid_price,
    status,
    remark,
    admin_remark,
    is_deleted,
    created_at,
    updated_at,
    reviewed_at,
    reviewed_by,
    batch_id
FROM quotes
WHERE id = $1 AND is_deleted = FALSE
LIMIT 1
`

// 根據ID獲取單一報價記錄
// 僅返回未被刪除的報價
func (q *Queries) GetQuoteByID(ctx context.Context, id uint32) (*Quote, error) {
	row := q.db.QueryRow(ctx, getQuoteByID, id)
	var i Quote
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.ProductID,
		&i.UserID,
		&i.QuoteType,
		&i.MarketPrice,
		&i.InternetPrice,
		&i.OriginalPrice,
		&i.PromotionPrice,
		&i.BidPrice,
		&i.SameAsBidPrice,
		&i.Status,
		&i.Remark,
		&i.AdminRemark,
		&i.IsDeleted,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.ReviewedBy,
		&i.BatchID,
	)
	return &i, err
}

const getQuoteStatsByProjectID = `-- name: GetQuoteStatsByProjectID :many
WITH quote_counts AS (
    SELECT
        quote_type,
        status,
        COUNT(*) as count
    FROM quotes
    WHERE
        project_id = $1 AND
        is_deleted = FALSE
    GROUP BY quote_type, status
),
     company_stats AS (
         SELECT
             COUNT(DISTINCT c.id) as total_companies,
             COUNT(DISTINCT CASE WHEN EXISTS (
                 SELECT 1 FROM quotes q
                                   JOIN users u ON q.user_id = u.id
                 WHERE u.company_id = c.id AND q.project_id = $1 AND q.is_deleted = FALSE
             ) THEN c.id END) as quoted_companies
         FROM companies c
                  JOIN users u ON u.company_id = c.id
         WHERE u.status = '通過' AND c.is_deleted = FALSE
         GROUP BY c.id
     ),
     product_stats AS (
         SELECT
             COUNT(DISTINCT p.id) as total_products,
             COUNT(DISTINCT CASE WHEN EXISTS (
                 SELECT 1 FROM quotes q
                 WHERE q.product_id = p.id AND q.project_id = $1 AND q.is_deleted = FALSE
             ) THEN p.id END) as quoted_products
         FROM products p
                  JOIN product_groups pg ON p.group_id = pg.id
         WHERE pg.project_id = $1 AND p.is_deleted = FALSE
     )
SELECT
    quote_type,
    status,
    count,
    (SELECT COALESCE(SUM(total_companies), 0) FROM company_stats) as total_companies,
    (SELECT COALESCE(SUM(quoted_companies), 0) FROM company_stats) as quoted_companies,
    (SELECT total_products FROM product_stats) as total_products,
    (SELECT quoted_products FROM product_stats) as quoted_products
FROM quote_counts
ORDER BY quote_type, status
`

type GetQuoteStatsByProjectIDRow struct {
	QuoteType       QuoteType      `json:"quoteType"`
	Status          QuoteStatus    `json:"status"`
	Count           *int64         `json:"count"`
	TotalCompanies  pgtype.Numeric `json:"totalCompanies"`
	QuotedCompanies pgtype.Numeric `json:"quotedCompanies"`
	TotalProducts   *int64         `json:"totalProducts"`
	QuotedProducts  *int64         `json:"quotedProducts"`
}

func (q *Queries) GetQuoteStatsByProjectID(ctx context.Context, dollar_1 *int32) ([]*GetQuoteStatsByProjectIDRow, error) {
	rows, err := q.db.Query(ctx, getQuoteStatsByProjectID, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetQuoteStatsByProjectIDRow{}
	for rows.Next() {
		var i GetQuoteStatsByProjectIDRow
		if err := rows.Scan(
			&i.QuoteType,
			&i.Status,
			&i.Count,
			&i.TotalCompanies,
			&i.QuotedCompanies,
			&i.TotalProducts,
			&i.QuotedProducts,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getQuotesByProjectAndProduct = `-- name: GetQuotesByProjectAndProduct :many
SELECT
    id,
    project_id,
    product_id,
    user_id,
    quote_type,
    market_price,
    internet_price,
    original_price,
    promotion_price,
    bid_price,
    same_as_bid_price,
    status,
    remark,
    admin_remark,
    is_deleted,
    created_at,
    updated_at,
    reviewed_at,
    reviewed_by,
    batch_id
FROM quotes
WHERE
    project_id = $1 AND
    product_id = $2 AND
    is_deleted = FALSE
ORDER BY created_at ASC
`

type GetQuotesByProjectAndProductParams struct {
	ProjectID uint32 `json:"projectId"`
	ProductID uint32 `json:"productId"`
}

// 根據專案ID和產品ID獲取報價記錄列表
// 按創建時間升序排序，用於顯示產品報價歷史
func (q *Queries) GetQuotesByProjectAndProduct(ctx context.Context, arg GetQuotesByProjectAndProductParams) ([]*Quote, error) {
	rows, err := q.db.Query(ctx, getQuotesByProjectAndProduct, arg.ProjectID, arg.ProductID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Quote{}
	for rows.Next() {
		var i Quote
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.ProductID,
			&i.UserID,
			&i.QuoteType,
			&i.MarketPrice,
			&i.InternetPrice,
			&i.OriginalPrice,
			&i.PromotionPrice,
			&i.BidPrice,
			&i.SameAsBidPrice,
			&i.Status,
			&i.Remark,
			&i.AdminRemark,
			&i.IsDeleted,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ReviewedAt,
			&i.ReviewedBy,
			&i.BatchID,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getQuotesByProjectAndUser = `-- name: GetQuotesByProjectAndUser :many
SELECT
    id,
    project_id,
    product_id,
    user_id,
    quote_type,
    market_price,
    internet_price,
    original_price,
    promotion_price,
    bid_price,
    same_as_bid_price,
    status,
    remark,
    admin_remark,
    is_deleted,
    created_at,
    updated_at,
    reviewed_at,
    reviewed_by,
    batch_id
FROM quotes
WHERE
    project_id = $1 AND
    user_id = $2 AND
    is_deleted = FALSE
ORDER BY created_at DESC
`

type GetQuotesByProjectAndUserParams struct {
	ProjectID uint32 `json:"projectId"`
	UserID    uint32 `json:"userId"`
}

// 根據專案ID和用戶ID獲取報價記錄列表
// 按創建時間降序排序，用於顯示用戶最近的報價活動
func (q *Queries) GetQuotesByProjectAndUser(ctx context.Context, arg GetQuotesByProjectAndUserParams) ([]*Quote, error) {
	rows, err := q.db.Query(ctx, getQuotesByProjectAndUser, arg.ProjectID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Quote{}
	for rows.Next() {
		var i Quote
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.ProductID,
			&i.UserID,
			&i.QuoteType,
			&i.MarketPrice,
			&i.InternetPrice,
			&i.OriginalPrice,
			&i.PromotionPrice,
			&i.BidPrice,
			&i.SameAsBidPrice,
			&i.Status,
			&i.Remark,
			&i.AdminRemark,
			&i.IsDeleted,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ReviewedAt,
			&i.ReviewedBy,
			&i.BatchID,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const hasCompanyQuote = `-- name: HasCompanyQuote :one
SELECT EXISTS (
    SELECT 1
    FROM quotes q
             JOIN users u ON q.user_id = u.id
    WHERE u.company_id = $2 AND q.project_id = $1 AND q.is_deleted = false AND q.quote_type = '廠商報價'
)
`

type HasCompanyQuoteParams struct {
	ProjectID uint32 `json:"projectId"`
	CompanyID uint32 `json:"companyId"`
}

func (q *Queries) HasCompanyQuote(ctx context.Context, arg HasCompanyQuoteParams) (bool, error) {
	row := q.db.QueryRow(ctx, hasCompanyQuote, arg.ProjectID, arg.CompanyID)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const listQuotes = `-- name: ListQuotes :many
SELECT
    q.id,
    q.project_id,
    q.product_id,
    q.user_id,
    q.quote_type,
    q.market_price,
    q.internet_price,
    q.original_price,
    q.promotion_price,
    q.bid_price,
    q.same_as_bid_price,
    q.status,
    q.remark,
    q.admin_remark,
    q.is_deleted,
    q.created_at,
    q.updated_at,
    q.reviewed_at,
    q.reviewed_by,
    q.batch_id
FROM quotes q
         JOIN products p ON q.product_id = p.id
         JOIN product_groups pg ON p.group_id = pg.id
         JOIN users u ON q.user_id = u.id
         LEFT JOIN companies c ON u.company_id = c.id
WHERE
    q.is_deleted = FALSE AND
    CASE WHEN $1::bigint > 0 THEN q.project_id = $1 ELSE TRUE END AND
    CASE WHEN $2::bigint > 0 THEN q.product_id = $2 ELSE TRUE END AND
    CASE WHEN $3::bigint > 0 THEN q.user_id = $3 ELSE TRUE END AND
    CASE WHEN $4::bigint > 0 THEN c.id = $4 ELSE TRUE END AND
    CASE WHEN $5::text != '' THEN q.quote_type = $5::quote_type ELSE TRUE END AND
    CASE WHEN $6::text != '' THEN q.status = $6::quote_status ELSE TRUE END AND
    CASE WHEN $7::bigint > 0 THEN p.group_id = $7 ELSE TRUE END AND
    CASE WHEN $8::text != '' THEN
             p.name ILIKE '%' || $8 || '%' OR
             u.username ILIKE '%' || $8 || '%' OR
             COALESCE(c.company_name, '') ILIKE '%' || $8 || '%'
         ELSE TRUE END
ORDER BY
    CASE WHEN $9::text = 'created_at' AND $10::text = 'asc' THEN q.created_at END ASC,
    CASE WHEN $9::text = 'created_at' AND $10::text = 'desc' THEN q.created_at END DESC,
    CASE WHEN $9::text = 'updated_at' AND $10::text = 'asc' THEN q.updated_at END ASC,
    CASE WHEN $9::text = 'updated_at' AND $10::text = 'desc' THEN q.updated_at END DESC,
    CASE WHEN $9::text = 'reviewed_at' AND $10::text = 'asc' THEN q.reviewed_at END ASC,
    CASE WHEN $9::text = 'reviewed_at' AND $10::text = 'desc' THEN q.reviewed_at END DESC,
    q.created_at DESC
LIMIT CASE
          WHEN $12::int > 0 THEN $12::int
          WHEN $12 IS NULL THEN NULL
          ELSE 2147483647 -- PostgreSQL 的最大整數，實際上相當於無限制
    END
    OFFSET $11::int
`

type ListQuotesParams struct {
	ProjectID  *int64  `json:"projectId"`
	ProductID  *int64  `json:"productId"`
	UserID     *int64  `json:"userId"`
	CompanyID  *int64  `json:"companyId"`
	QuoteType  *string `json:"quoteType"`
	Status     *string `json:"status"`
	GroupID    *int64  `json:"groupId"`
	SearchTerm *string `json:"searchTerm"`
	SortBy     *string `json:"sortBy"`
	SortDir    *string `json:"sortDir"`
	OffsetVal  *int32  `json:"offsetVal"`
	LimitVal   *int32  `json:"limitVal"`
}

type ListQuotesRow struct {
	ID             uint32      `json:"id"`
	ProjectID      uint32      `json:"projectId"`
	ProductID      uint32      `json:"productId"`
	UserID         uint32      `json:"userId"`
	QuoteType      QuoteType   `json:"quoteType"`
	MarketPrice    interface{} `json:"marketPrice"`
	InternetPrice  interface{} `json:"internetPrice"`
	OriginalPrice  interface{} `json:"originalPrice"`
	PromotionPrice interface{} `json:"promotionPrice"`
	BidPrice       interface{} `json:"bidPrice"`
	SameAsBidPrice *bool       `json:"sameAsBidPrice"`
	Status         QuoteStatus `json:"status"`
	Remark         *string     `json:"remark"`
	AdminRemark    *string     `json:"adminRemark"`
	IsDeleted      bool        `json:"isDeleted"`
	CreatedAt      time.Time   `json:"createdAt"`
	UpdatedAt      time.Time   `json:"updatedAt"`
	ReviewedAt     time.Time   `json:"reviewedAt"`
	ReviewedBy     uint32      `json:"reviewedBy"`
	BatchID        uint32      `json:"batchId"`
}

func (q *Queries) ListQuotes(ctx context.Context, arg ListQuotesParams) ([]*ListQuotesRow, error) {
	rows, err := q.db.Query(ctx, listQuotes,
		arg.ProjectID,
		arg.ProductID,
		arg.UserID,
		arg.CompanyID,
		arg.QuoteType,
		arg.Status,
		arg.GroupID,
		arg.SearchTerm,
		arg.SortBy,
		arg.SortDir,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ListQuotesRow{}
	for rows.Next() {
		var i ListQuotesRow
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.ProductID,
			&i.UserID,
			&i.QuoteType,
			&i.MarketPrice,
			&i.InternetPrice,
			&i.OriginalPrice,
			&i.PromotionPrice,
			&i.BidPrice,
			&i.SameAsBidPrice,
			&i.Status,
			&i.Remark,
			&i.AdminRemark,
			&i.IsDeleted,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ReviewedAt,
			&i.ReviewedBy,
			&i.BatchID,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateQuote = `-- name: UpdateQuote :one
UPDATE quotes
SET
    market_price = COALESCE($2, market_price),
    internet_price = COALESCE($3, internet_price),
    original_price = COALESCE($4, original_price),
    promotion_price = COALESCE($5, promotion_price),
    bid_price = COALESCE($6, bid_price),
    same_as_bid_price = COALESCE($7, same_as_bid_price),
    status = COALESCE($8::quote_status, status),
    remark = COALESCE($9, remark),
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND is_deleted = FALSE
RETURNING
    id, project_id, product_id, user_id, quote_type,
    market_price, internet_price, original_price, promotion_price, bid_price,
    same_as_bid_price, status, remark, admin_remark, is_deleted,
    created_at, updated_at, reviewed_at, reviewed_by, batch_id
`

type UpdateQuoteParams struct {
	ID             uint32          `json:"id"`
	MarketPrice    pgtype.Numeric  `json:"marketPrice"`
	InternetPrice  pgtype.Numeric  `json:"internetPrice"`
	OriginalPrice  pgtype.Numeric  `json:"originalPrice"`
	PromotionPrice pgtype.Numeric  `json:"promotionPrice"`
	BidPrice       pgtype.Numeric  `json:"bidPrice"`
	SameAsBidPrice *bool           `json:"sameAsBidPrice"`
	Status         NullQuoteStatus `json:"status"`
	Remark         *string         `json:"remark"`
}

func (q *Queries) UpdateQuote(ctx context.Context, arg UpdateQuoteParams) (*Quote, error) {
	row := q.db.QueryRow(ctx, updateQuote,
		arg.ID,
		arg.MarketPrice,
		arg.InternetPrice,
		arg.OriginalPrice,
		arg.PromotionPrice,
		arg.BidPrice,
		arg.SameAsBidPrice,
		arg.Status,
		arg.Remark,
	)
	var i Quote
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.ProductID,
		&i.UserID,
		&i.QuoteType,
		&i.MarketPrice,
		&i.InternetPrice,
		&i.OriginalPrice,
		&i.PromotionPrice,
		&i.BidPrice,
		&i.SameAsBidPrice,
		&i.Status,
		&i.Remark,
		&i.AdminRemark,
		&i.IsDeleted,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.ReviewedBy,
		&i.BatchID,
	)
	return &i, err
}

const updateQuoteStatus = `-- name: UpdateQuoteStatus :one
UPDATE quotes
SET
    status = $2::quote_status,
    admin_remark = $3,
    reviewed_by = $4,
    reviewed_at = CURRENT_TIMESTAMP,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND is_deleted = FALSE
RETURNING
    id, project_id, product_id, user_id, quote_type,
    market_price, internet_price, original_price, promotion_price, bid_price,
    same_as_bid_price, status, remark, admin_remark, is_deleted,
    created_at, updated_at, reviewed_at, reviewed_by, batch_id
`

type UpdateQuoteStatusParams struct {
	ID          uint32      `json:"id"`
	Column2     QuoteStatus `json:"column2"`
	AdminRemark *string     `json:"adminRemark"`
	ReviewedBy  uint32      `json:"reviewedBy"`
}

func (q *Queries) UpdateQuoteStatus(ctx context.Context, arg UpdateQuoteStatusParams) (*Quote, error) {
	row := q.db.QueryRow(ctx, updateQuoteStatus,
		arg.ID,
		arg.Column2,
		arg.AdminRemark,
		arg.ReviewedBy,
	)
	var i Quote
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.ProductID,
		&i.UserID,
		&i.QuoteType,
		&i.MarketPrice,
		&i.InternetPrice,
		&i.OriginalPrice,
		&i.PromotionPrice,
		&i.BidPrice,
		&i.SameAsBidPrice,
		&i.Status,
		&i.Remark,
		&i.AdminRemark,
		&i.IsDeleted,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.ReviewedBy,
		&i.BatchID,
	)
	return &i, err
}
