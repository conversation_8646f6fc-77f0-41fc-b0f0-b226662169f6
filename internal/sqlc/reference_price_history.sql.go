// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: reference_price_history.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createReferencePriceHistory = `-- name: CreateReferencePriceHistory :one
INSERT INTO reference_price_history (
    reference_price_id,
    original_price,
    adjusted_price,
    remark,
    created_by
) VALUES (
             $1, $2, $3, $4, $5
         )
RETURNING
    id, reference_price_id, original_price, adjusted_price, remark, created_at, created_by
`

type CreateReferencePriceHistoryParams struct {
	ReferencePriceID uint32         `json:"referencePriceId"`
	OriginalPrice    pgtype.Numeric `json:"originalPrice"`
	AdjustedPrice    pgtype.Numeric `json:"adjustedPrice"`
	Remark           *string        `json:"remark"`
	CreatedBy        uint32         `json:"createdBy"`
}

func (q *Queries) CreateReferencePriceHistory(ctx context.Context, arg CreateReferencePriceHistoryParams) (*ReferencePriceHistory, error) {
	row := q.db.QueryRow(ctx, createReferencePriceHistory,
		arg.ReferencePriceID,
		arg.OriginalPrice,
		arg.AdjustedPrice,
		arg.Remark,
		arg.CreatedBy,
	)
	var i ReferencePriceHistory
	err := row.Scan(
		&i.ID,
		&i.ReferencePriceID,
		&i.OriginalPrice,
		&i.AdjustedPrice,
		&i.Remark,
		&i.CreatedAt,
		&i.CreatedBy,
	)
	return &i, err
}

const listReferencePriceHistoriesByReferencePriceID = `-- name: ListReferencePriceHistoriesByReferencePriceID :many
SELECT
    id,
    reference_price_id,
    original_price,
    adjusted_price,
    remark,
    created_at,
    created_by
FROM reference_price_history
WHERE reference_price_id = $1
ORDER BY created_at DESC
`

func (q *Queries) ListReferencePriceHistoriesByReferencePriceID(ctx context.Context, referencePriceID uint32) ([]*ReferencePriceHistory, error) {
	rows, err := q.db.Query(ctx, listReferencePriceHistoriesByReferencePriceID, referencePriceID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ReferencePriceHistory{}
	for rows.Next() {
		var i ReferencePriceHistory
		if err := rows.Scan(
			&i.ID,
			&i.ReferencePriceID,
			&i.OriginalPrice,
			&i.AdjustedPrice,
			&i.Remark,
			&i.CreatedAt,
			&i.CreatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
