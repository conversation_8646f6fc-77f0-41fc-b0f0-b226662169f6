// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: project_log.sql

package sqlc

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type BatchCreateProjectLogsParams struct {
	UserID            uint32             `json:"userId"`
	UnifiedBusinessNo *string            `json:"unifiedBusinessNo"`
	ProductGroup      *string            `json:"productGroup"`
	ItemNo            *string            `json:"itemNo"`
	Pid               int32              `json:"pid"`
	Category          *string            `json:"category"`
	LogType           NullProjectLogType `json:"logType"`
	Message           *string            `json:"message"`
	ProductID         uint32             `json:"productId"`
	ProjectID         uint32             `json:"projectId"`
}

const countProjectLogs = `-- name: CountProjectLogs :one
SELECT COUNT(id)
FROM project_logs
WHERE
    CASE WHEN $1::bigint > 0 THEN user_id = $1 ELSE TRUE END AND
    CASE WHEN $2::bigint > 0 THEN project_id = $2 ELSE TRUE END AND
    CASE WHEN $3::bigint > 0 THEN product_id = $3 ELSE TRUE END AND
    CASE WHEN $4::text != '' THEN unified_business_no = $4 ELSE TRUE END AND
    CASE WHEN $5::text != '' THEN product_group ILIKE '%' || $5 || '%' ELSE TRUE END AND
    CASE WHEN $6::text != '' THEN item_no = $6 ELSE TRUE END AND
    CASE WHEN $7::text != '' THEN category = $7 ELSE TRUE END AND
    CASE WHEN $8::text != '' THEN log_type = $8::project_log_type ELSE TRUE END AND
    CASE WHEN $9::timestamp IS NOT NULL THEN created_at >= $9 ELSE TRUE END AND
    CASE WHEN $10::timestamp IS NOT NULL THEN created_at <= $10 ELSE TRUE END AND
    CASE WHEN $11::text != '' THEN
             message ILIKE '%' || $11 || '%' OR
             unified_business_no ILIKE '%' || $11 || '%' OR
             product_group ILIKE '%' || $11 || '%' OR
             item_no ILIKE '%' || $11 || '%'
         ELSE TRUE END
`

type CountProjectLogsParams struct {
	UserID            int64            `json:"userId"`
	ProjectID         int64            `json:"projectId"`
	ProductID         int64            `json:"productId"`
	UnifiedBusinessNo string           `json:"unifiedBusinessNo"`
	ProductGroup      string           `json:"productGroup"`
	ItemNo            string           `json:"itemNo"`
	Category          string           `json:"category"`
	LogType           string           `json:"logType"`
	FromDate          pgtype.Timestamp `json:"fromDate"`
	ToDate            pgtype.Timestamp `json:"toDate"`
	SearchTerm        string           `json:"searchTerm"`
}

func (q *Queries) CountProjectLogs(ctx context.Context, arg CountProjectLogsParams) (int64, error) {
	row := q.db.QueryRow(ctx, countProjectLogs,
		arg.UserID,
		arg.ProjectID,
		arg.ProductID,
		arg.UnifiedBusinessNo,
		arg.ProductGroup,
		arg.ItemNo,
		arg.Category,
		arg.LogType,
		arg.FromDate,
		arg.ToDate,
		arg.SearchTerm,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createProjectLog = `-- name: CreateProjectLog :one
INSERT INTO project_logs (
    user_id,
    unified_business_no,
    product_group,
    item_no,
    category,
    log_type,
    message,
    product_id,
    project_id
) VALUES (
             $1, $2, $3, $4, $5, $6, $7, $8, $9
         )
RETURNING
    id, user_id, unified_business_no, product_group, item_no, category,
    log_type, message, product_id, project_id, created_at
`

type CreateProjectLogParams struct {
	UserID            uint32             `json:"userId"`
	UnifiedBusinessNo *string            `json:"unifiedBusinessNo"`
	ProductGroup      *string            `json:"productGroup"`
	ItemNo            *string            `json:"itemNo"`
	Category          *string            `json:"category"`
	LogType           NullProjectLogType `json:"logType"`
	Message           *string            `json:"message"`
	ProductID         uint32             `json:"productId"`
	ProjectID         uint32             `json:"projectId"`
}

type CreateProjectLogRow struct {
	ID                uint32             `json:"id"`
	UserID            uint32             `json:"userId"`
	UnifiedBusinessNo *string            `json:"unifiedBusinessNo"`
	ProductGroup      *string            `json:"productGroup"`
	ItemNo            *string            `json:"itemNo"`
	Category          *string            `json:"category"`
	LogType           NullProjectLogType `json:"logType"`
	Message           *string            `json:"message"`
	ProductID         uint32             `json:"productId"`
	ProjectID         uint32             `json:"projectId"`
	CreatedAt         time.Time          `json:"createdAt"`
}

func (q *Queries) CreateProjectLog(ctx context.Context, arg CreateProjectLogParams) (*CreateProjectLogRow, error) {
	row := q.db.QueryRow(ctx, createProjectLog,
		arg.UserID,
		arg.UnifiedBusinessNo,
		arg.ProductGroup,
		arg.ItemNo,
		arg.Category,
		arg.LogType,
		arg.Message,
		arg.ProductID,
		arg.ProjectID,
	)
	var i CreateProjectLogRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.UnifiedBusinessNo,
		&i.ProductGroup,
		&i.ItemNo,
		&i.Category,
		&i.LogType,
		&i.Message,
		&i.ProductID,
		&i.ProjectID,
		&i.CreatedAt,
	)
	return &i, err
}

const listProjectLogs = `-- name: ListProjectLogs :many
SELECT
    id,
    user_id,
    unified_business_no,
    product_group,
    item_no,
    pid,
    category,
    log_type,
    message,
    product_id,
    project_id,
    created_at
FROM project_logs
WHERE
    CASE WHEN $1::bigint > 0 THEN user_id = $1 ELSE TRUE END AND
    CASE WHEN $2::bigint > 0 THEN project_id = $2 ELSE TRUE END AND
    CASE WHEN $3::bigint > 0 THEN product_id = $3 ELSE TRUE END AND
    CASE WHEN $4::text != '' THEN unified_business_no = $4 ELSE TRUE END AND
    CASE WHEN $5::text != '' THEN product_group ILIKE '%' || $5 || '%' ELSE TRUE END AND
    CASE WHEN $6::text != '' THEN item_no = $6 ELSE TRUE END AND
    CASE WHEN $7::text != '' THEN category = $7 ELSE TRUE END AND
    CASE WHEN $8::text != '' THEN log_type = $8::project_log_type ELSE TRUE END AND
    CASE WHEN $9::timestamp IS NOT NULL THEN created_at >= $9 ELSE TRUE END AND
    CASE WHEN $10::timestamp IS NOT NULL THEN created_at <= $10 ELSE TRUE END AND
    CASE WHEN $11::text != '' THEN
             message ILIKE '%' || $11 || '%' OR
             unified_business_no ILIKE '%' || $11 || '%' OR
             product_group ILIKE '%' || $11 || '%' OR
             item_no ILIKE '%' || $11 || '%'
         ELSE TRUE END
ORDER BY
    CASE WHEN $12::text = 'asc' THEN created_at END ASC,
    CASE WHEN $12::text = 'desc' OR $12::text = '' THEN created_at END DESC
LIMIT CASE
          WHEN $14::int > 0 THEN $14::int
          WHEN $14 IS NULL THEN NULL
          ELSE 2147483647 -- PostgreSQL 的最大整數，實際上相當於無限制
END
OFFSET $13::int
`

type ListProjectLogsParams struct {
	UserID            int64            `json:"userId"`
	ProjectID         int64            `json:"projectId"`
	ProductID         int64            `json:"productId"`
	UnifiedBusinessNo string           `json:"unifiedBusinessNo"`
	ProductGroup      string           `json:"productGroup"`
	ItemNo            string           `json:"itemNo"`
	Category          string           `json:"category"`
	LogType           string           `json:"logType"`
	FromDate          pgtype.Timestamp `json:"fromDate"`
	ToDate            pgtype.Timestamp `json:"toDate"`
	SearchTerm        string           `json:"searchTerm"`
	SortDir           string           `json:"sortDir"`
	OffsetVal         int32            `json:"offsetVal"`
	LimitVal          int32            `json:"limitVal"`
}

func (q *Queries) ListProjectLogs(ctx context.Context, arg ListProjectLogsParams) ([]*ProjectLog, error) {
	rows, err := q.db.Query(ctx, listProjectLogs,
		arg.UserID,
		arg.ProjectID,
		arg.ProductID,
		arg.UnifiedBusinessNo,
		arg.ProductGroup,
		arg.ItemNo,
		arg.Category,
		arg.LogType,
		arg.FromDate,
		arg.ToDate,
		arg.SearchTerm,
		arg.SortDir,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ProjectLog{}
	for rows.Next() {
		var i ProjectLog
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.UnifiedBusinessNo,
			&i.ProductGroup,
			&i.ItemNo,
			&i.Pid,
			&i.Category,
			&i.LogType,
			&i.Message,
			&i.ProductID,
			&i.ProjectID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listProjectLogsByProjectID = `-- name: ListProjectLogsByProjectID :many
SELECT
    id,
    user_id,
    unified_business_no,
    product_group,
    item_no,
    pid,
    category,
    log_type,
    message,
    product_id,
    project_id,
    created_at
FROM project_logs
WHERE project_id = $1
ORDER BY created_at DESC
LIMIT $2
    OFFSET $3
`

type ListProjectLogsByProjectIDParams struct {
	ProjectID uint32 `json:"projectId"`
	Limit     int64  `json:"limit"`
	Offset    int64  `json:"offset"`
}

func (q *Queries) ListProjectLogsByProjectID(ctx context.Context, arg ListProjectLogsByProjectIDParams) ([]*ProjectLog, error) {
	rows, err := q.db.Query(ctx, listProjectLogsByProjectID, arg.ProjectID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ProjectLog{}
	for rows.Next() {
		var i ProjectLog
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.UnifiedBusinessNo,
			&i.ProductGroup,
			&i.ItemNo,
			&i.Pid,
			&i.Category,
			&i.LogType,
			&i.Message,
			&i.ProductID,
			&i.ProjectID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
