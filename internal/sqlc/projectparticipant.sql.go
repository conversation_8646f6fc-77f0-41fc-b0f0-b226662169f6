// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: projectparticipant.sql

package sqlc

import (
	"context"
)

const listCompanyUserIDsByProject = `-- name: ListCompanyUserIDsByProject :many
SELECT DISTINCT q.user_id
FROM quotes q
WHERE q.project_id = $1 AND q.is_deleted = false AND q.quote_type = '廠商報價'
ORDER BY q.user_id
`

func (q *Queries) ListCompanyUserIDsByProject(ctx context.Context, projectID uint32) ([]uint32, error) {
	rows, err := q.db.Query(ctx, listCompanyUserIDsByProject, projectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []uint32{}
	for rows.Next() {
		var user_id uint32
		if err := rows.Scan(&user_id); err != nil {
			return nil, err
		}
		items = append(items, user_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listProjectIDsByParticipant = `-- name: ListProjectIDsByParticipant :many
SELECT DISTINCT project_id
FROM quotes
WHERE user_id = $1 AND is_deleted = false AND quote_type = '廠商報價'
ORDER BY project_id
`

// 查詢用戶參與的專案ID列表
func (q *Queries) ListProjectIDsByParticipant(ctx context.Context, userID uint32) ([]uint32, error) {
	rows, err := q.db.Query(ctx, listProjectIDsByParticipant, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []uint32{}
	for rows.Next() {
		var project_id uint32
		if err := rows.Scan(&project_id); err != nil {
			return nil, err
		}
		items = append(items, project_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
