// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: contracted_vendor.sql

package sqlc

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type BatchCreateContractedVendorsParams struct {
	ProjectID     uint32           `json:"projectId"`
	CompanyID     uint32           `json:"companyId"`
	ProductID     uint32           `json:"productId"`
	ContractPrice pgtype.Numeric   `json:"contractPrice"`
	StartDate     pgtype.Timestamp `json:"startDate"`
	EndDate       pgtype.Timestamp `json:"endDate"`
}

const checkIsContracted = `-- name: CheckIsContracted :one
SELECT EXISTS (
    SELECT 1
    FROM contracted_vendors
    WHERE
        company_id = $1 AND
        product_id = $2 AND
        (end_date IS NULL OR end_date > CURRENT_TIMESTAMP)
) AS is_contracted
`

type CheckIsContractedParams struct {
	CompanyID uint32 `json:"companyId"`
	ProductID uint32 `json:"productId"`
}

func (q *Queries) CheckIsContracted(ctx context.Context, arg CheckIsContractedParams) (bool, error) {
	row := q.db.QueryRow(ctx, checkIsContracted, arg.CompanyID, arg.ProductID)
	var is_contracted bool
	err := row.Scan(&is_contracted)
	return is_contracted, err
}

const createContractedVendor = `-- name: CreateContractedVendor :one
INSERT INTO contracted_vendors (
    project_id,
    company_id,
    product_id,
    contract_price,
    start_date,
    end_date
) VALUES (
             $1, $2, $3, $4, $5, $6
         )
RETURNING
    id, project_id, company_id, product_id, contract_price,
    start_date, end_date, created_at, updated_at
`

type CreateContractedVendorParams struct {
	ProjectID     uint32           `json:"projectId"`
	CompanyID     uint32           `json:"companyId"`
	ProductID     uint32           `json:"productId"`
	ContractPrice pgtype.Numeric   `json:"contractPrice"`
	StartDate     pgtype.Timestamp `json:"startDate"`
	EndDate       pgtype.Timestamp `json:"endDate"`
}

func (q *Queries) CreateContractedVendor(ctx context.Context, arg CreateContractedVendorParams) (*ContractedVendor, error) {
	row := q.db.QueryRow(ctx, createContractedVendor,
		arg.ProjectID,
		arg.CompanyID,
		arg.ProductID,
		arg.ContractPrice,
		arg.StartDate,
		arg.EndDate,
	)
	var i ContractedVendor
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.CompanyID,
		&i.ProductID,
		&i.ContractPrice,
		&i.StartDate,
		&i.EndDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const deleteContractedVendor = `-- name: DeleteContractedVendor :exec
DELETE FROM contracted_vendors
WHERE id = $1
`

func (q *Queries) DeleteContractedVendor(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteContractedVendor, id)
	return err
}

const getContractedVendorByID = `-- name: GetContractedVendorByID :one
SELECT
    id,
    project_id,
    company_id,
    product_id,
    contract_price,
    start_date,
    end_date,
    created_at,
    updated_at
FROM contracted_vendors
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetContractedVendorByID(ctx context.Context, id uint32) (*ContractedVendor, error) {
	row := q.db.QueryRow(ctx, getContractedVendorByID, id)
	var i ContractedVendor
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.CompanyID,
		&i.ProductID,
		&i.ContractPrice,
		&i.StartDate,
		&i.EndDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getContractedVendorByUniqueConstraint = `-- name: GetContractedVendorByUniqueConstraint :one
SELECT
    id,
    project_id,
    company_id,
    product_id,
    contract_price,
    start_date,
    end_date,
    created_at,
    updated_at
FROM contracted_vendors
WHERE
    project_id = $1 AND
    company_id = $2 AND
    product_id = $3
LIMIT 1
`

type GetContractedVendorByUniqueConstraintParams struct {
	ProjectID uint32 `json:"projectId"`
	CompanyID uint32 `json:"companyId"`
	ProductID uint32 `json:"productId"`
}

func (q *Queries) GetContractedVendorByUniqueConstraint(ctx context.Context, arg GetContractedVendorByUniqueConstraintParams) (*ContractedVendor, error) {
	row := q.db.QueryRow(ctx, getContractedVendorByUniqueConstraint, arg.ProjectID, arg.CompanyID, arg.ProductID)
	var i ContractedVendor
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.CompanyID,
		&i.ProductID,
		&i.ContractPrice,
		&i.StartDate,
		&i.EndDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const listContractedVendorsByCompanyID = `-- name: ListContractedVendorsByCompanyID :many
SELECT
    cv.id,
    cv.project_id,
    cv.company_id,
    cv.product_id,
    cv.contract_price,
    cv.start_date,
    cv.end_date,
    cv.created_at,
    cv.updated_at,
    c.company_name,
    c.unified_business_no
FROM contracted_vendors cv
         JOIN companies c ON cv.company_id = c.id
WHERE
    cv.company_id = $1 AND
    (cv.end_date IS NULL OR cv.end_date > CURRENT_TIMESTAMP)
ORDER BY cv.start_date DESC
`

type ListContractedVendorsByCompanyIDRow struct {
	ID                uint32           `json:"id"`
	ProjectID         uint32           `json:"projectId"`
	CompanyID         uint32           `json:"companyId"`
	ProductID         uint32           `json:"productId"`
	ContractPrice     pgtype.Numeric   `json:"contractPrice"`
	StartDate         pgtype.Timestamp `json:"startDate"`
	EndDate           pgtype.Timestamp `json:"endDate"`
	CreatedAt         time.Time        `json:"createdAt"`
	UpdatedAt         time.Time        `json:"updatedAt"`
	CompanyName       string           `json:"companyName"`
	UnifiedBusinessNo string           `json:"unifiedBusinessNo"`
}

func (q *Queries) ListContractedVendorsByCompanyID(ctx context.Context, companyID uint32) ([]*ListContractedVendorsByCompanyIDRow, error) {
	rows, err := q.db.Query(ctx, listContractedVendorsByCompanyID, companyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ListContractedVendorsByCompanyIDRow{}
	for rows.Next() {
		var i ListContractedVendorsByCompanyIDRow
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.CompanyID,
			&i.ProductID,
			&i.ContractPrice,
			&i.StartDate,
			&i.EndDate,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CompanyName,
			&i.UnifiedBusinessNo,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listContractedVendorsByProductID = `-- name: ListContractedVendorsByProductID :many
SELECT
    cv.id,
    cv.project_id,
    cv.company_id,
    cv.product_id,
    cv.contract_price,
    cv.start_date,
    cv.end_date,
    cv.created_at,
    cv.updated_at,
    c.company_name,
    c.unified_business_no
FROM contracted_vendors cv
         JOIN companies c ON cv.company_id = c.id
WHERE
    cv.product_id = $1 AND
    (cv.end_date IS NULL OR cv.end_date > CURRENT_TIMESTAMP)
ORDER BY cv.start_date DESC
`

type ListContractedVendorsByProductIDRow struct {
	ID                uint32           `json:"id"`
	ProjectID         uint32           `json:"projectId"`
	CompanyID         uint32           `json:"companyId"`
	ProductID         uint32           `json:"productId"`
	ContractPrice     pgtype.Numeric   `json:"contractPrice"`
	StartDate         pgtype.Timestamp `json:"startDate"`
	EndDate           pgtype.Timestamp `json:"endDate"`
	CreatedAt         time.Time        `json:"createdAt"`
	UpdatedAt         time.Time        `json:"updatedAt"`
	CompanyName       string           `json:"companyName"`
	UnifiedBusinessNo string           `json:"unifiedBusinessNo"`
}

func (q *Queries) ListContractedVendorsByProductID(ctx context.Context, productID uint32) ([]*ListContractedVendorsByProductIDRow, error) {
	rows, err := q.db.Query(ctx, listContractedVendorsByProductID, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ListContractedVendorsByProductIDRow{}
	for rows.Next() {
		var i ListContractedVendorsByProductIDRow
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.CompanyID,
			&i.ProductID,
			&i.ContractPrice,
			&i.StartDate,
			&i.EndDate,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CompanyName,
			&i.UnifiedBusinessNo,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listContractedVendorsByProjectID = `-- name: ListContractedVendorsByProjectID :many
    SELECT
        id,
        project_id,
        company_id,
        product_id,
        contract_price,
        start_date,
        end_date,
        created_at,
        updated_at
    FROM contracted_vendors
    WHERE project_id = $1
    ORDER BY start_date DESC
`

func (q *Queries) ListContractedVendorsByProjectID(ctx context.Context, projectID uint32) ([]*ContractedVendor, error) {
	rows, err := q.db.Query(ctx, listContractedVendorsByProjectID, projectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ContractedVendor{}
	for rows.Next() {
		var i ContractedVendor
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.CompanyID,
			&i.ProductID,
			&i.ContractPrice,
			&i.StartDate,
			&i.EndDate,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateContractedVendor = `-- name: UpdateContractedVendor :one
UPDATE contracted_vendors
SET
    contract_price = COALESCE($2, contract_price),
    start_date = COALESCE($3, start_date),
    end_date = COALESCE($4, end_date),
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING
    id, project_id, company_id, product_id, contract_price,
    start_date, end_date, created_at, updated_at
`

type UpdateContractedVendorParams struct {
	ID            uint32           `json:"id"`
	ContractPrice pgtype.Numeric   `json:"contractPrice"`
	StartDate     pgtype.Timestamp `json:"startDate"`
	EndDate       pgtype.Timestamp `json:"endDate"`
}

func (q *Queries) UpdateContractedVendor(ctx context.Context, arg UpdateContractedVendorParams) (*ContractedVendor, error) {
	row := q.db.QueryRow(ctx, updateContractedVendor,
		arg.ID,
		arg.ContractPrice,
		arg.StartDate,
		arg.EndDate,
	)
	var i ContractedVendor
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.CompanyID,
		&i.ProductID,
		&i.ContractPrice,
		&i.StartDate,
		&i.EndDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
