// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: company.sql

package sqlc

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const countCompanies = `-- name: CountCompanies :one
SELECT COUNT(DISTINCT c.id)
FROM companies c
         LEFT JOIN users u ON c.id = u.company_id AND (
    CASE WHEN $1::text != ''
             THEN u.status = $1::user_status END
    )
WHERE
    c.is_deleted = FALSE AND
    CASE WHEN $2::text != '' THEN c.company_type = $2::company_type ELSE TRUE END AND
    CASE WHEN $3::text != '' THEN
             c.company_name ILIKE '%' || $3 || '%' OR
             c.unified_business_no ILIKE '%' || $3 || '%' OR
             c.owner ILIKE '%' || $3 || '%'
         ELSE TRUE END
`

type CountCompaniesParams struct {
	UserStatus  string `json:"userStatus"`
	CompanyType string `json:"companyType"`
	SearchTerm  string `json:"searchTerm"`
}

func (q *Queries) CountCompanies(ctx context.Context, arg CountCompaniesParams) (int64, error) {
	row := q.db.QueryRow(ctx, countCompanies, arg.UserStatus, arg.CompanyType, arg.SearchTerm)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createCompany = `-- name: CreateCompany :one
INSERT INTO companies (
    company_name,
    unified_business_no,
    owner,
    phone,
    address,
    company_type,
    is_contact_confirmed
) VALUES (
             $1, $2, $3, $4, $5, $6, $7
         )
ON CONFLICT (unified_business_no)
    DO UPDATE SET
    unified_business_no = EXCLUDED.unified_business_no -- 實際上不會更新
RETURNING id, company_name, unified_business_no, owner, phone, address, company_type,
    is_contact_confirmed, last_contact_confirmed_at, created_at, updated_at,
    (xmax = 0) AS was_inserted
`

type CreateCompanyParams struct {
	CompanyName        string          `json:"companyName"`
	UnifiedBusinessNo  string          `json:"unifiedBusinessNo"`
	Owner              string          `json:"owner"`
	Phone              *string         `json:"phone"`
	Address            *string         `json:"address"`
	CompanyType        NullCompanyType `json:"companyType"`
	IsContactConfirmed bool            `json:"isContactConfirmed"`
}

type CreateCompanyRow struct {
	ID                     uint32           `json:"id"`
	CompanyName            string           `json:"companyName"`
	UnifiedBusinessNo      string           `json:"unifiedBusinessNo"`
	Owner                  string           `json:"owner"`
	Phone                  *string          `json:"phone"`
	Address                *string          `json:"address"`
	CompanyType            NullCompanyType  `json:"companyType"`
	IsContactConfirmed     bool             `json:"isContactConfirmed"`
	LastContactConfirmedAt pgtype.Timestamp `json:"lastContactConfirmedAt"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
	WasInserted            bool             `json:"wasInserted"`
}

func (q *Queries) CreateCompany(ctx context.Context, arg CreateCompanyParams) (*CreateCompanyRow, error) {
	row := q.db.QueryRow(ctx, createCompany,
		arg.CompanyName,
		arg.UnifiedBusinessNo,
		arg.Owner,
		arg.Phone,
		arg.Address,
		arg.CompanyType,
		arg.IsContactConfirmed,
	)
	var i CreateCompanyRow
	err := row.Scan(
		&i.ID,
		&i.CompanyName,
		&i.UnifiedBusinessNo,
		&i.Owner,
		&i.Phone,
		&i.Address,
		&i.CompanyType,
		&i.IsContactConfirmed,
		&i.LastContactConfirmedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.WasInserted,
	)
	return &i, err
}

const deleteCompany = `-- name: DeleteCompany :one
UPDATE companies
SET
    is_deleted = TRUE,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, company_name, unified_business_no, owner, phone,  address, company_type,
    is_contact_confirmed, last_contact_confirmed_at, created_at, updated_at
`

type DeleteCompanyRow struct {
	ID                     uint32           `json:"id"`
	CompanyName            string           `json:"companyName"`
	UnifiedBusinessNo      string           `json:"unifiedBusinessNo"`
	Owner                  string           `json:"owner"`
	Phone                  *string          `json:"phone"`
	Address                *string          `json:"address"`
	CompanyType            NullCompanyType  `json:"companyType"`
	IsContactConfirmed     bool             `json:"isContactConfirmed"`
	LastContactConfirmedAt pgtype.Timestamp `json:"lastContactConfirmedAt"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
}

func (q *Queries) DeleteCompany(ctx context.Context, id uint32) (*DeleteCompanyRow, error) {
	row := q.db.QueryRow(ctx, deleteCompany, id)
	var i DeleteCompanyRow
	err := row.Scan(
		&i.ID,
		&i.CompanyName,
		&i.UnifiedBusinessNo,
		&i.Owner,
		&i.Phone,
		&i.Address,
		&i.CompanyType,
		&i.IsContactConfirmed,
		&i.LastContactConfirmedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getCompanyByID = `-- name: GetCompanyByID :one
SELECT
    id,
    company_name,
    unified_business_no,
    owner,
    phone,
    address,
    company_type,
    is_contact_confirmed,
    last_contact_confirmed_at,
    created_at,
    updated_at
FROM companies
WHERE id = $1 AND is_deleted = FALSE
LIMIT 1
`

type GetCompanyByIDRow struct {
	ID                     uint32           `json:"id"`
	CompanyName            string           `json:"companyName"`
	UnifiedBusinessNo      string           `json:"unifiedBusinessNo"`
	Owner                  string           `json:"owner"`
	Phone                  *string          `json:"phone"`
	Address                *string          `json:"address"`
	CompanyType            NullCompanyType  `json:"companyType"`
	IsContactConfirmed     bool             `json:"isContactConfirmed"`
	LastContactConfirmedAt pgtype.Timestamp `json:"lastContactConfirmedAt"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
}

func (q *Queries) GetCompanyByID(ctx context.Context, id uint32) (*GetCompanyByIDRow, error) {
	row := q.db.QueryRow(ctx, getCompanyByID, id)
	var i GetCompanyByIDRow
	err := row.Scan(
		&i.ID,
		&i.CompanyName,
		&i.UnifiedBusinessNo,
		&i.Owner,
		&i.Phone,
		&i.Address,
		&i.CompanyType,
		&i.IsContactConfirmed,
		&i.LastContactConfirmedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getCompanyByUnifiedBusinessNo = `-- name: GetCompanyByUnifiedBusinessNo :one
SELECT
    id,
    company_name,
    unified_business_no,
    owner,
    phone,
    address,
    company_type,
    is_contact_confirmed,
    last_contact_confirmed_at,
    created_at,
    updated_at
FROM companies
WHERE unified_business_no = $1 AND is_deleted = FALSE
LIMIT 1
`

type GetCompanyByUnifiedBusinessNoRow struct {
	ID                     uint32           `json:"id"`
	CompanyName            string           `json:"companyName"`
	UnifiedBusinessNo      string           `json:"unifiedBusinessNo"`
	Owner                  string           `json:"owner"`
	Phone                  *string          `json:"phone"`
	Address                *string          `json:"address"`
	CompanyType            NullCompanyType  `json:"companyType"`
	IsContactConfirmed     bool             `json:"isContactConfirmed"`
	LastContactConfirmedAt pgtype.Timestamp `json:"lastContactConfirmedAt"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
}

func (q *Queries) GetCompanyByUnifiedBusinessNo(ctx context.Context, unifiedBusinessNo string) (*GetCompanyByUnifiedBusinessNoRow, error) {
	row := q.db.QueryRow(ctx, getCompanyByUnifiedBusinessNo, unifiedBusinessNo)
	var i GetCompanyByUnifiedBusinessNoRow
	err := row.Scan(
		&i.ID,
		&i.CompanyName,
		&i.UnifiedBusinessNo,
		&i.Owner,
		&i.Phone,
		&i.Address,
		&i.CompanyType,
		&i.IsContactConfirmed,
		&i.LastContactConfirmedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getCompanyByUserID = `-- name: GetCompanyByUserID :one
SELECT
    c.id,
    c.company_name,
    c.unified_business_no,
    c.owner,
    c.phone,
    c.address,
    c.company_type,
    c.is_contact_confirmed,
    c.last_contact_confirmed_at,
    c.created_at,
    c.updated_at
FROM companies c
         JOIN users u ON c.id = u.company_id
WHERE u.id = $1 AND c.is_deleted = FALSE
LIMIT 1
`

type GetCompanyByUserIDRow struct {
	ID                     uint32           `json:"id"`
	CompanyName            string           `json:"companyName"`
	UnifiedBusinessNo      string           `json:"unifiedBusinessNo"`
	Owner                  string           `json:"owner"`
	Phone                  *string          `json:"phone"`
	Address                *string          `json:"address"`
	CompanyType            NullCompanyType  `json:"companyType"`
	IsContactConfirmed     bool             `json:"isContactConfirmed"`
	LastContactConfirmedAt pgtype.Timestamp `json:"lastContactConfirmedAt"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
}

func (q *Queries) GetCompanyByUserID(ctx context.Context, id uint32) (*GetCompanyByUserIDRow, error) {
	row := q.db.QueryRow(ctx, getCompanyByUserID, id)
	var i GetCompanyByUserIDRow
	err := row.Scan(
		&i.ID,
		&i.CompanyName,
		&i.UnifiedBusinessNo,
		&i.Owner,
		&i.Phone,
		&i.Address,
		&i.CompanyType,
		&i.IsContactConfirmed,
		&i.LastContactConfirmedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getCompanyByUserIDWithUser = `-- name: GetCompanyByUserIDWithUser :one
SELECT
    c.id,
    c.company_name,
    c.unified_business_no,
    c.owner,
    c.phone,
    c.address,
    c.company_type,
    c.is_contact_confirmed,
    c.last_contact_confirmed_at,
    c.created_at,
    c.updated_at,
    u.status as user_status,
    u.username as username
FROM companies c
         JOIN users u ON c.id = u.company_id
WHERE u.id = $1 AND c.is_deleted = FALSE
LIMIT 1
`

type GetCompanyByUserIDWithUserRow struct {
	ID                     uint32           `json:"id"`
	CompanyName            string           `json:"companyName"`
	UnifiedBusinessNo      string           `json:"unifiedBusinessNo"`
	Owner                  string           `json:"owner"`
	Phone                  *string          `json:"phone"`
	Address                *string          `json:"address"`
	CompanyType            NullCompanyType  `json:"companyType"`
	IsContactConfirmed     bool             `json:"isContactConfirmed"`
	LastContactConfirmedAt pgtype.Timestamp `json:"lastContactConfirmedAt"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
	UserStatus             UserStatus       `json:"userStatus"`
	Username               string           `json:"username"`
}

func (q *Queries) GetCompanyByUserIDWithUser(ctx context.Context, id uint32) (*GetCompanyByUserIDWithUserRow, error) {
	row := q.db.QueryRow(ctx, getCompanyByUserIDWithUser, id)
	var i GetCompanyByUserIDWithUserRow
	err := row.Scan(
		&i.ID,
		&i.CompanyName,
		&i.UnifiedBusinessNo,
		&i.Owner,
		&i.Phone,
		&i.Address,
		&i.CompanyType,
		&i.IsContactConfirmed,
		&i.LastContactConfirmedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UserStatus,
		&i.Username,
	)
	return &i, err
}

const getCompanyUsers = `-- name: GetCompanyUsers :many
SELECT
    u.id,
    u.username,
    u.email,
    u.job_title,
    u.user_role,
    u.status,
    u.created_at,
    u.updated_at,
    u.last_login_at
FROM users u
WHERE u.company_id = $1
ORDER BY u.created_at ASC
`

type GetCompanyUsersRow struct {
	ID          uint32     `json:"id"`
	Username    string     `json:"username"`
	Email       string     `json:"email"`
	JobTitle    *string    `json:"jobTitle"`
	UserRole    UserRole   `json:"userRole"`
	Status      UserStatus `json:"status"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   time.Time  `json:"updatedAt"`
	LastLoginAt time.Time  `json:"lastLoginAt"`
}

func (q *Queries) GetCompanyUsers(ctx context.Context, companyID uint32) ([]*GetCompanyUsersRow, error) {
	rows, err := q.db.Query(ctx, getCompanyUsers, companyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetCompanyUsersRow{}
	for rows.Next() {
		var i GetCompanyUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.Email,
			&i.JobTitle,
			&i.UserRole,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LastLoginAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listByProject = `-- name: ListByProject :many
SELECT
    c.id,
    c.company_name,
    c.unified_business_no,
    c.owner,
    c.phone,
    c.address,
    c.company_type,
    c.is_contact_confirmed,
    c.is_deleted,
    c.last_contact_confirmed_at,
    c.created_at,
    c.updated_at
FROM companies c
         JOIN contracted_vendors cv ON c.id = cv.company_id
WHERE cv.project_id = $1
ORDER BY c.company_name
`

func (q *Queries) ListByProject(ctx context.Context, projectID uint32) ([]*Company, error) {
	rows, err := q.db.Query(ctx, listByProject, projectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Company{}
	for rows.Next() {
		var i Company
		if err := rows.Scan(
			&i.ID,
			&i.CompanyName,
			&i.UnifiedBusinessNo,
			&i.Owner,
			&i.Phone,
			&i.Address,
			&i.CompanyType,
			&i.IsContactConfirmed,
			&i.IsDeleted,
			&i.LastContactConfirmedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listCompanies = `-- name: ListCompanies :many
WITH company_user_counts AS (
    SELECT
        c.id,
        c.company_name,
        c.unified_business_no,
        c.owner,
        c.phone,
        c.address,
        c.company_type,
        c.is_contact_confirmed,
        c.last_contact_confirmed_at,
        c.created_at,
        c.updated_at,
        COUNT(CASE WHEN u.status = $7::user_status OR $7::text = '' THEN 1 END) as matching_users
    FROM companies c
             LEFT JOIN users u ON c.id = u.company_id
    WHERE c.is_deleted = FALSE
    GROUP BY c.id
    HAVING
        ($7::text = '' OR COUNT(CASE WHEN u.status = $7::user_status THEN 1 END) > 0)
)
SELECT id, company_name, unified_business_no, owner, phone, address, company_type, is_contact_confirmed, last_contact_confirmed_at, created_at, updated_at, matching_users
FROM company_user_counts
WHERE
    ($1::text = '' OR company_type = $1::company_type) AND
    ($2::text = '' OR
     company_name ILIKE '%' || $2 || '%' OR
     unified_business_no ILIKE '%' || $2 || '%' OR
     owner ILIKE '%' || $2 || '%')
ORDER BY
    CASE WHEN $3::text = 'created_at' AND $4::text = 'asc' THEN created_at END ASC,
    CASE WHEN $3::text = 'created_at' AND $4::text = 'desc' THEN created_at END DESC,
    CASE WHEN $3::text = 'company_name' AND $4::text = 'asc' THEN company_name END ASC,
    CASE WHEN $3::text = 'company_name' AND $4::text = 'desc' THEN company_name END DESC,
    created_at DESC
LIMIT $6::int OFFSET $5::int
`

type ListCompaniesParams struct {
	CompanyType string     `json:"companyType"`
	SearchTerm  string     `json:"searchTerm"`
	SortBy      string     `json:"sortBy"`
	SortDir     string     `json:"sortDir"`
	OffsetVal   int32      `json:"offsetVal"`
	LimitVal    int32      `json:"limitVal"`
	UserStatus  UserStatus `json:"userStatus"`
}

type ListCompaniesRow struct {
	ID                     uint32           `json:"id"`
	CompanyName            string           `json:"companyName"`
	UnifiedBusinessNo      string           `json:"unifiedBusinessNo"`
	Owner                  string           `json:"owner"`
	Phone                  *string          `json:"phone"`
	Address                *string          `json:"address"`
	CompanyType            NullCompanyType  `json:"companyType"`
	IsContactConfirmed     bool             `json:"isContactConfirmed"`
	LastContactConfirmedAt pgtype.Timestamp `json:"lastContactConfirmedAt"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
	MatchingUsers          int64            `json:"matchingUsers"`
}

func (q *Queries) ListCompanies(ctx context.Context, arg ListCompaniesParams) ([]*ListCompaniesRow, error) {
	rows, err := q.db.Query(ctx, listCompanies,
		arg.CompanyType,
		arg.SearchTerm,
		arg.SortBy,
		arg.SortDir,
		arg.OffsetVal,
		arg.LimitVal,
		arg.UserStatus,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ListCompaniesRow{}
	for rows.Next() {
		var i ListCompaniesRow
		if err := rows.Scan(
			&i.ID,
			&i.CompanyName,
			&i.UnifiedBusinessNo,
			&i.Owner,
			&i.Phone,
			&i.Address,
			&i.CompanyType,
			&i.IsContactConfirmed,
			&i.LastContactConfirmedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.MatchingUsers,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listCompaniesByUserIDs = `-- name: ListCompaniesByUserIDs :many
SELECT
    c.id,
    c.company_name,
    c.unified_business_no,
    c.owner,
    c.phone,
    c.address,
    c.company_type,
    c.is_contact_confirmed,
    c.is_deleted,
    c.last_contact_confirmed_at,
    c.created_at,
    c.updated_at
FROM companies c
         JOIN users u ON c.id = u.company_id
WHERE u.id = ANY($1::int[])
GROUP BY c.id
ORDER BY c.company_name
`

func (q *Queries) ListCompaniesByUserIDs(ctx context.Context, dollar_1 []int32) ([]*Company, error) {
	rows, err := q.db.Query(ctx, listCompaniesByUserIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Company{}
	for rows.Next() {
		var i Company
		if err := rows.Scan(
			&i.ID,
			&i.CompanyName,
			&i.UnifiedBusinessNo,
			&i.Owner,
			&i.Phone,
			&i.Address,
			&i.CompanyType,
			&i.IsContactConfirmed,
			&i.IsDeleted,
			&i.LastContactConfirmedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateCompany = `-- name: UpdateCompany :one
UPDATE companies
SET
    company_name = COALESCE($2, company_name),
    owner = COALESCE($3, owner),
    phone = COALESCE($4, phone),
    address = COALESCE($5, address),
    company_type = COALESCE($6::company_type, company_type),
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND is_deleted = FALSE
RETURNING id, company_name, unified_business_no, owner, phone,address, company_type,
    is_contact_confirmed, last_contact_confirmed_at, created_at, updated_at
`

type UpdateCompanyParams struct {
	ID          uint32          `json:"id"`
	CompanyName *string         `json:"companyName"`
	Owner       *string         `json:"owner"`
	Phone       *string         `json:"phone"`
	Address     *string         `json:"address"`
	CompanyType NullCompanyType `json:"companyType"`
}

type UpdateCompanyRow struct {
	ID                     uint32           `json:"id"`
	CompanyName            string           `json:"companyName"`
	UnifiedBusinessNo      string           `json:"unifiedBusinessNo"`
	Owner                  string           `json:"owner"`
	Phone                  *string          `json:"phone"`
	Address                *string          `json:"address"`
	CompanyType            NullCompanyType  `json:"companyType"`
	IsContactConfirmed     bool             `json:"isContactConfirmed"`
	LastContactConfirmedAt pgtype.Timestamp `json:"lastContactConfirmedAt"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
}

func (q *Queries) UpdateCompany(ctx context.Context, arg UpdateCompanyParams) (*UpdateCompanyRow, error) {
	row := q.db.QueryRow(ctx, updateCompany,
		arg.ID,
		arg.CompanyName,
		arg.Owner,
		arg.Phone,
		arg.Address,
		arg.CompanyType,
	)
	var i UpdateCompanyRow
	err := row.Scan(
		&i.ID,
		&i.CompanyName,
		&i.UnifiedBusinessNo,
		&i.Owner,
		&i.Phone,
		&i.Address,
		&i.CompanyType,
		&i.IsContactConfirmed,
		&i.LastContactConfirmedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const updateContactConfirmation = `-- name: UpdateContactConfirmation :one
UPDATE companies
SET
    is_contact_confirmed = $2,
    last_contact_confirmed_at = CASE WHEN $2 = TRUE THEN CURRENT_TIMESTAMP ELSE last_contact_confirmed_at END,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND is_deleted = FALSE
RETURNING id, company_name, unified_business_no, owner, phone, address, company_type,
    is_contact_confirmed, last_contact_confirmed_at, created_at, updated_at
`

type UpdateContactConfirmationParams struct {
	ID                 uint32 `json:"id"`
	IsContactConfirmed bool   `json:"isContactConfirmed"`
}

type UpdateContactConfirmationRow struct {
	ID                     uint32           `json:"id"`
	CompanyName            string           `json:"companyName"`
	UnifiedBusinessNo      string           `json:"unifiedBusinessNo"`
	Owner                  string           `json:"owner"`
	Phone                  *string          `json:"phone"`
	Address                *string          `json:"address"`
	CompanyType            NullCompanyType  `json:"companyType"`
	IsContactConfirmed     bool             `json:"isContactConfirmed"`
	LastContactConfirmedAt pgtype.Timestamp `json:"lastContactConfirmedAt"`
	CreatedAt              time.Time        `json:"createdAt"`
	UpdatedAt              time.Time        `json:"updatedAt"`
}

func (q *Queries) UpdateContactConfirmation(ctx context.Context, arg UpdateContactConfirmationParams) (*UpdateContactConfirmationRow, error) {
	row := q.db.QueryRow(ctx, updateContactConfirmation, arg.ID, arg.IsContactConfirmed)
	var i UpdateContactConfirmationRow
	err := row.Scan(
		&i.ID,
		&i.CompanyName,
		&i.UnifiedBusinessNo,
		&i.Owner,
		&i.Phone,
		&i.Address,
		&i.CompanyType,
		&i.IsContactConfirmed,
		&i.LastContactConfirmedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
