// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: quote_batch_rejection.sql

package sqlc

import (
	"context"
)

const createBatchQuoteRejection = `-- name: CreateBatchQuoteRejection :one
INSERT INTO batch_quote_rejections (
    project_id,
    rejection_reason,
    rejected_count,
    created_by,
    remark
) VALUES (
             $1, $2, $3, $4, $5
         )
RETURNING
    id, project_id, rejection_reason, rejected_count, created_at, created_by, remark
`

type CreateBatchQuoteRejectionParams struct {
	ProjectID       uint32  `json:"projectId"`
	RejectionReason string  `json:"rejectionReason"`
	RejectedCount   uint32  `json:"rejectedCount"`
	CreatedBy       uint32  `json:"createdBy"`
	Remark          *string `json:"remark"`
}

func (q *Queries) CreateBatchQuoteRejection(ctx context.Context, arg CreateBatchQuoteRejectionParams) (*BatchQuoteRejection, error) {
	row := q.db.QueryRow(ctx, createBatchQuoteRejection,
		arg.ProjectID,
		arg.RejectionReason,
		arg.RejectedCount,
		arg.CreatedBy,
		arg.Remark,
	)
	var i BatchQuoteRejection
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.RejectionReason,
		&i.RejectedCount,
		&i.CreatedAt,
		&i.CreatedBy,
		&i.Remark,
	)
	return &i, err
}

const getBatchQuoteRejectionByID = `-- name: GetBatchQuoteRejectionByID :one
SELECT
    id,
    project_id,
    rejection_reason,
    rejected_count,
    created_at,
    created_by,
    remark
FROM batch_quote_rejections
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetBatchQuoteRejectionByID(ctx context.Context, id uint32) (*BatchQuoteRejection, error) {
	row := q.db.QueryRow(ctx, getBatchQuoteRejectionByID, id)
	var i BatchQuoteRejection
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.RejectionReason,
		&i.RejectedCount,
		&i.CreatedAt,
		&i.CreatedBy,
		&i.Remark,
	)
	return &i, err
}

const getRejectedQuotesByBatchID = `-- name: GetRejectedQuotesByBatchID :many
SELECT
    q.id,
    q.project_id,
    q.product_id,
    q.user_id,
    q.quote_type,
    q.market_price,
    q.internet_price,
    q.original_price,
    q.promotion_price,
    q.bid_price,
    q.same_as_bid_price,
    q.status,
    q.remark,
    q.admin_remark,
    q.is_deleted,
    q.created_at,
    q.updated_at,
    q.reviewed_at,
    q.reviewed_by,
    q.batch_id
FROM quotes q
WHERE
    q.batch_id = $1 AND
    q.is_deleted = FALSE
ORDER BY q.id ASC
`

func (q *Queries) GetRejectedQuotesByBatchID(ctx context.Context, batchID uint32) ([]*Quote, error) {
	rows, err := q.db.Query(ctx, getRejectedQuotesByBatchID, batchID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Quote{}
	for rows.Next() {
		var i Quote
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.ProductID,
			&i.UserID,
			&i.QuoteType,
			&i.MarketPrice,
			&i.InternetPrice,
			&i.OriginalPrice,
			&i.PromotionPrice,
			&i.BidPrice,
			&i.SameAsBidPrice,
			&i.Status,
			&i.Remark,
			&i.AdminRemark,
			&i.IsDeleted,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ReviewedAt,
			&i.ReviewedBy,
			&i.BatchID,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listBatchQuoteRejectionsByProjectID = `-- name: ListBatchQuoteRejectionsByProjectID :many
SELECT
    id,
    project_id,
    rejection_reason,
    rejected_count,
    created_at,
    created_by,
    remark
FROM batch_quote_rejections
WHERE project_id = $1
ORDER BY created_at DESC
`

func (q *Queries) ListBatchQuoteRejectionsByProjectID(ctx context.Context, projectID uint32) ([]*BatchQuoteRejection, error) {
	rows, err := q.db.Query(ctx, listBatchQuoteRejectionsByProjectID, projectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*BatchQuoteRejection{}
	for rows.Next() {
		var i BatchQuoteRejection
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.RejectionReason,
			&i.RejectedCount,
			&i.CreatedAt,
			&i.CreatedBy,
			&i.Remark,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateBatchQuoteRejection = `-- name: UpdateBatchQuoteRejection :one
UPDATE batch_quote_rejections
SET
    rejection_reason = COALESCE($2, rejection_reason),
    rejected_count = COALESCE($3, rejected_count),
    remark = COALESCE($4, remark)
WHERE id = $1
RETURNING
    id, project_id, rejection_reason, rejected_count, created_at, created_by, remark
`

type UpdateBatchQuoteRejectionParams struct {
	ID              uint32  `json:"id"`
	RejectionReason *string `json:"rejectionReason"`
	RejectedCount   uint32  `json:"rejectedCount"`
	Remark          *string `json:"remark"`
}

func (q *Queries) UpdateBatchQuoteRejection(ctx context.Context, arg UpdateBatchQuoteRejectionParams) (*BatchQuoteRejection, error) {
	row := q.db.QueryRow(ctx, updateBatchQuoteRejection,
		arg.ID,
		arg.RejectionReason,
		arg.RejectedCount,
		arg.Remark,
	)
	var i BatchQuoteRejection
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.RejectionReason,
		&i.RejectedCount,
		&i.CreatedAt,
		&i.CreatedBy,
		&i.Remark,
	)
	return &i, err
}
