// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: company_info_change_request.sql

package sqlc

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const countCompanyInfoChangeRequestsByStatus = `-- name: CountCompanyInfoChangeRequestsByStatus :one
/*
    統計特定廠商各狀態的資料異動申請數量
    - 使用場景: 廠商資料面板顯示各類申請統計數字
    - 效能優化: 
      * 使用COUNT(1)替代COUNT(*)，理論上更高效
      * 使用FILTER子句進行高效的條件統計
    - 索引依賴: company_id欄位應有索引
*/
SELECT
    COUNT(1) FILTER (WHERE status = '待審') AS pending_count,
    COUNT(1) FILTER (WHERE status = '通過') AS approved_count,
    COUNT(1) FILTER (WHERE status = '退件') AS rejected_count,
    COUNT(1) AS total_count
FROM company_info_change_requests
WHERE company_id = $1
`

type CountCompanyInfoChangeRequestsByStatusRow struct {
	PendingCount  int64 `json:"pendingCount"`
	ApprovedCount int64 `json:"approvedCount"`
	RejectedCount int64 `json:"rejectedCount"`
	TotalCount    int64 `json:"totalCount"`
}

func (q *Queries) CountCompanyInfoChangeRequestsByStatus(ctx context.Context, companyID uint32) (*CountCompanyInfoChangeRequestsByStatusRow, error) {
	row := q.db.QueryRow(ctx, countCompanyInfoChangeRequestsByStatus, companyID)
	var i CountCompanyInfoChangeRequestsByStatusRow
	err := row.Scan(
		&i.PendingCount,
		&i.ApprovedCount,
		&i.RejectedCount,
		&i.TotalCount,
	)
	return &i, err
}

const countCompanyInfoChangeRequestsWithFilters = `-- name: CountCompanyInfoChangeRequestsWithFilters :one
SELECT COUNT(*)
FROM company_info_change_requests cicr
         LEFT JOIN companies c ON cicr.company_id = c.id
WHERE
  -- 使用COALESCE處理空值過濾條件
    (COALESCE($1::INTEGER, 0) = 0 OR cicr.company_id = $1::INTEGER) AND
    ($2::info_change_status IS NULL OR cicr.status = $2::info_change_status) AND
    (COALESCE($3::TIMESTAMPTZ, '1900-01-01') <= cicr.created_at) AND
    (COALESCE($4::TIMESTAMPTZ, '2099-12-31') >= cicr.created_at) AND
    (COALESCE($5::INTEGER, 0) = 0 OR cicr.reviewed_by = $5::INTEGER) AND
    (
        $6::TEXT = '' OR
        c.company_name ILIKE '%' || $6::TEXT || '%' OR
        c.unified_business_no ILIKE '%' || $6::TEXT || '%' OR
        cicr.remark ILIKE '%' || $6::TEXT || '%'
        )
`

type CountCompanyInfoChangeRequestsWithFiltersParams struct {
	CompanyID     *int32               `json:"companyId"`
	Status        NullInfoChangeStatus `json:"status"`
	CreatedAfter  pgtype.Timestamptz   `json:"createdAfter"`
	CreatedBefore pgtype.Timestamptz   `json:"createdBefore"`
	ReviewedBy    *int32               `json:"reviewedBy"`
	SearchTerm    *string              `json:"searchTerm"`
}

// 獲取符合過濾條件的資料異動申請總數
func (q *Queries) CountCompanyInfoChangeRequestsWithFilters(ctx context.Context, arg CountCompanyInfoChangeRequestsWithFiltersParams) (int64, error) {
	row := q.db.QueryRow(ctx, countCompanyInfoChangeRequestsWithFilters,
		arg.CompanyID,
		arg.Status,
		arg.CreatedAfter,
		arg.CreatedBefore,
		arg.ReviewedBy,
		arg.SearchTerm,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createCompanyInfoChangeRequest = `-- name: CreateCompanyInfoChangeRequest :one

/*
    創建一筆廠商資料異動申請
    - 使用場景: 廠商申請修改其基本資料時創建申請記錄
    - 業務規則: 初始狀態應為'待審'，由管理員後續審核
    - 效能優化: 在RETURNING子句中明確指定需要的欄位，避免返回所有欄位
*/
INSERT INTO company_info_change_requests (
    company_id,
    original_data,
    new_data,
    status,
    remark,
    created_by
) VALUES (
    $1, $2, $3, $4, $5, $6
)
RETURNING 
    id, 
    company_id, 
    status, 
    created_at, 
    created_by
`

type CreateCompanyInfoChangeRequestParams struct {
	CompanyID    uint32           `json:"companyId"`
	OriginalData []byte           `json:"originalData"`
	NewData      []byte           `json:"newData"`
	Status       InfoChangeStatus `json:"status"`
	Remark       *string          `json:"remark"`
	CreatedBy    uint32           `json:"createdBy"`
}

type CreateCompanyInfoChangeRequestRow struct {
	ID        uint32           `json:"id"`
	CompanyID uint32           `json:"companyId"`
	Status    InfoChangeStatus `json:"status"`
	CreatedAt time.Time        `json:"createdAt"`
	CreatedBy uint32           `json:"createdBy"`
}

// 添加合理的限制，避免超大結果集
func (q *Queries) CreateCompanyInfoChangeRequest(ctx context.Context, arg CreateCompanyInfoChangeRequestParams) (*CreateCompanyInfoChangeRequestRow, error) {
	row := q.db.QueryRow(ctx, createCompanyInfoChangeRequest,
		arg.CompanyID,
		arg.OriginalData,
		arg.NewData,
		arg.Status,
		arg.Remark,
		arg.CreatedBy,
	)
	var i CreateCompanyInfoChangeRequestRow
	err := row.Scan(
		&i.ID,
		&i.CompanyID,
		&i.Status,
		&i.CreatedAt,
		&i.CreatedBy,
	)
	return &i, err
}

const createCompanyInfoChangeRequestWithTx = `-- name: CreateCompanyInfoChangeRequestWithTx :one
INSERT INTO company_info_change_requests (
    company_id,
    original_data,
    new_data,
    status,
    remark,
    created_by
) VALUES (
             $1, $2, $3, $4, $5, $6
         )
RETURNING id, company_id, original_data, new_data, status, remark, review_remark, created_at, updated_at, reviewed_at, created_by, reviewed_by
`

type CreateCompanyInfoChangeRequestWithTxParams struct {
	CompanyID    uint32           `json:"companyId"`
	OriginalData []byte           `json:"originalData"`
	NewData      []byte           `json:"newData"`
	Status       InfoChangeStatus `json:"status"`
	Remark       *string          `json:"remark"`
	CreatedBy    uint32           `json:"createdBy"`
}

// 在事務中創建資料異動申請
func (q *Queries) CreateCompanyInfoChangeRequestWithTx(ctx context.Context, arg CreateCompanyInfoChangeRequestWithTxParams) (*CompanyInfoChangeRequest, error) {
	row := q.db.QueryRow(ctx, createCompanyInfoChangeRequestWithTx,
		arg.CompanyID,
		arg.OriginalData,
		arg.NewData,
		arg.Status,
		arg.Remark,
		arg.CreatedBy,
	)
	var i CompanyInfoChangeRequest
	err := row.Scan(
		&i.ID,
		&i.CompanyID,
		&i.OriginalData,
		&i.NewData,
		&i.Status,
		&i.Remark,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
		&i.CreatedBy,
		&i.ReviewedBy,
	)
	return &i, err
}

const deleteCompanyInfoChangeRequest = `-- name: DeleteCompanyInfoChangeRequest :exec
DELETE FROM company_info_change_requests
WHERE id = $1
`

// 刪除資料異動申請（管理員功能，通常不使用）
func (q *Queries) DeleteCompanyInfoChangeRequest(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteCompanyInfoChangeRequest, id)
	return err
}

const getCompanyInfoChangeRequestByID = `-- name: GetCompanyInfoChangeRequestByID :one
/*
    根據ID獲取單筆廠商資料異動申請
    - 索引依賴: 主鍵id欄位有默認索引確保快速查詢
    - 最佳實踐: 明確指定所需欄位而非使用SELECT *，可提高查詢效率並避免讀取不必要的大型欄位
*/
SELECT 
    id, 
    company_id, 
    original_data, 
    new_data, 
    status, 
    remark, 
    review_remark, 
    created_by, 
    reviewed_by, 
    created_at, 
    updated_at, 
    reviewed_at
FROM company_info_change_requests
WHERE id = $1
`

type GetCompanyInfoChangeRequestByIDRow struct {
	ID           uint32           `json:"id"`
	CompanyID    uint32           `json:"companyId"`
	OriginalData []byte           `json:"originalData"`
	NewData      []byte           `json:"newData"`
	Status       InfoChangeStatus `json:"status"`
	Remark       *string          `json:"remark"`
	ReviewRemark *string          `json:"reviewRemark"`
	CreatedBy    uint32           `json:"createdBy"`
	ReviewedBy   uint32           `json:"reviewedBy"`
	CreatedAt    time.Time        `json:"createdAt"`
	UpdatedAt    time.Time        `json:"updatedAt"`
	ReviewedAt   time.Time        `json:"reviewedAt"`
}

func (q *Queries) GetCompanyInfoChangeRequestByID(ctx context.Context, id uint32) (*GetCompanyInfoChangeRequestByIDRow, error) {
	row := q.db.QueryRow(ctx, getCompanyInfoChangeRequestByID, id)
	var i GetCompanyInfoChangeRequestByIDRow
	err := row.Scan(
		&i.ID,
		&i.CompanyID,
		&i.OriginalData,
		&i.NewData,
		&i.Status,
		&i.Remark,
		&i.ReviewRemark,
		&i.CreatedBy,
		&i.ReviewedBy,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
	)
	return &i, err
}

const getLatestCompanyInfoChangeRequest = `-- name: GetLatestCompanyInfoChangeRequest :one
/*
    獲取廠商最新的一筆資料異動申請
    - 使用場景: 檢查廠商是否有最近的資料異動申請，尤其是待審核的申請
    - 索引建議: 為(company_id, created_at)添加聯合索引以優化此查詢
    - 效能優化: 僅選擇需要的欄位，確保快速響應
*/
SELECT 
    id, 
    company_id, 
    status, 
    created_at, 
    updated_at, 
    reviewed_at
FROM company_info_change_requests
WHERE company_id = $1
ORDER BY created_at DESC
LIMIT 1
`

type GetLatestCompanyInfoChangeRequestRow struct {
	ID         uint32           `json:"id"`
	CompanyID  uint32           `json:"companyId"`
	Status     InfoChangeStatus `json:"status"`
	CreatedAt  time.Time        `json:"createdAt"`
	UpdatedAt  time.Time        `json:"updatedAt"`
	ReviewedAt time.Time        `json:"reviewedAt"`
}

func (q *Queries) GetLatestCompanyInfoChangeRequest(ctx context.Context, companyID uint32) (*GetLatestCompanyInfoChangeRequestRow, error) {
	row := q.db.QueryRow(ctx, getLatestCompanyInfoChangeRequest, companyID)
	var i GetLatestCompanyInfoChangeRequestRow
	err := row.Scan(
		&i.ID,
		&i.CompanyID,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ReviewedAt,
	)
	return &i, err
}

const hasPendingCompanyInfoChangeRequest = `-- name: HasPendingCompanyInfoChangeRequest :one
SELECT EXISTS(
    SELECT 1 FROM company_info_change_requests
    WHERE company_id = $1 AND status = '待審'
)
`

// 檢查廠商是否有待審核的資料異動申請
func (q *Queries) HasPendingCompanyInfoChangeRequest(ctx context.Context, companyID uint32) (bool, error) {
	row := q.db.QueryRow(ctx, hasPendingCompanyInfoChangeRequest, companyID)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const listCompanyInfoChangeRequestsByCompanyID = `-- name: ListCompanyInfoChangeRequestsByCompanyID :many
/*
    根據廠商ID獲取該廠商的資料異動申請，支援依狀態過濾
    - 索引建議: company_id和status欄位建議添加聯合索引 (company_id, status)
    - 效能優化: 明確列出所需欄位，避免使用SELECT *
    - 過濾條件優化: 使用更高效的AND條件替代CASE WHEN
*/
SELECT 
    id, 
    company_id, 
    original_data, 
    new_data, 
    status, 
    remark, 
    review_remark, 
    created_by, 
    reviewed_by, 
    created_at, 
    updated_at, 
    reviewed_at
FROM company_info_change_requests
WHERE company_id = $1
  AND ($2 = '' OR status::info_change_status = $2::info_change_status)
ORDER BY created_at DESC
`

type ListCompanyInfoChangeRequestsByCompanyIDParams struct {
	CompanyID uint32  `json:"companyId"`
	Status    *string `json:"status"`
}

type ListCompanyInfoChangeRequestsByCompanyIDRow struct {
	ID           uint32           `json:"id"`
	CompanyID    uint32           `json:"companyId"`
	OriginalData []byte           `json:"originalData"`
	NewData      []byte           `json:"newData"`
	Status       InfoChangeStatus `json:"status"`
	Remark       *string          `json:"remark"`
	ReviewRemark *string          `json:"reviewRemark"`
	CreatedBy    uint32           `json:"createdBy"`
	ReviewedBy   uint32           `json:"reviewedBy"`
	CreatedAt    time.Time        `json:"createdAt"`
	UpdatedAt    time.Time        `json:"updatedAt"`
	ReviewedAt   time.Time        `json:"reviewedAt"`
}

func (q *Queries) ListCompanyInfoChangeRequestsByCompanyID(ctx context.Context, arg ListCompanyInfoChangeRequestsByCompanyIDParams) ([]*ListCompanyInfoChangeRequestsByCompanyIDRow, error) {
	rows, err := q.db.Query(ctx, listCompanyInfoChangeRequestsByCompanyID, arg.CompanyID, arg.Status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ListCompanyInfoChangeRequestsByCompanyIDRow{}
	for rows.Next() {
		var i ListCompanyInfoChangeRequestsByCompanyIDRow
		if err := rows.Scan(
			&i.ID,
			&i.CompanyID,
			&i.OriginalData,
			&i.NewData,
			&i.Status,
			&i.Remark,
			&i.ReviewRemark,
			&i.CreatedBy,
			&i.ReviewedBy,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ReviewedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listCompanyInfoChangeRequestsWithFilters = `-- name: ListCompanyInfoChangeRequestsWithFilters :many
/*
    根據多種條件查詢廠商資料異動申請，支援靈活的過濾、排序和分頁
    - 使用場景: 管理員查詢和過濾資料異動申請的主要介面
    - 效能優化: 
      * 明確指定需要的欄位而非使用SELECT *
      * 使用高效的過濾條件表達式
      * 改進排序邏輯，減少CASE表達式的複雜性
    - 索引建議:
      * 為frequently_filtered欄位創建索引: status, created_at, reviewed_by
      * 確保company_id上有索引
*/
SELECT 
    cicr.id,
    cicr.company_id,
    c.company_name,  -- 額外選擇公司名稱以便於顯示
    cicr.status,
    cicr.created_at,
    cicr.reviewed_at,
    cicr.reviewed_by,
    cicr.original_data,
    cicr.new_data,
    cicr.remark,
    cicr.review_remark
FROM company_info_change_requests cicr
LEFT JOIN companies c ON cicr.company_id = c.id
WHERE
    -- 優化過濾條件表達式
    ($1::INTEGER IS NULL OR $1::INTEGER = 0 OR cicr.company_id = $1::INTEGER) AND
    ($2::info_change_status IS NULL OR cicr.status = $2::info_change_status) AND
    ($3::TIMESTAMPTZ IS NULL OR cicr.created_at >= $3::TIMESTAMPTZ) AND
    ($4::TIMESTAMPTZ IS NULL OR cicr.created_at <= $4::TIMESTAMPTZ) AND
    ($5::INTEGER IS NULL OR $5::INTEGER = 0 OR cicr.reviewed_by = $5::INTEGER) AND
    (
        $6::TEXT IS NULL OR 
        $6::TEXT = '' OR
        c.company_name ILIKE '%' || $6::TEXT || '%' OR
        c.unified_business_no ILIKE '%' || $6::TEXT || '%' OR
        cicr.remark ILIKE '%' || $6::TEXT || '%'
    )
ORDER BY
    -- 先處理created_at排序
    CASE WHEN $7::TEXT = 'created_at' THEN
        CASE WHEN $8::TEXT = 'asc' THEN cicr.created_at END
    END ASC NULLS LAST,
    CASE WHEN $7::TEXT = 'created_at' THEN
        CASE WHEN $8::TEXT = 'desc' OR $8::TEXT IS NULL THEN cicr.created_at END
    END DESC NULLS LAST,
    -- 處理company_name排序
    CASE WHEN $7::TEXT = 'company_name' THEN
        CASE WHEN $8::TEXT = 'asc' THEN c.company_name END
    END ASC NULLS LAST,
    CASE WHEN $7::TEXT = 'company_name' THEN
        CASE WHEN $8::TEXT = 'desc' THEN c.company_name END
    END DESC NULLS LAST,
    -- 預設排序 (當sort_by為空或為其他值時)
    CASE WHEN $7::TEXT IS NULL OR $7::TEXT = '' OR 
              ($7::TEXT != 'created_at' AND $7::TEXT != 'company_name') 
         THEN cicr.created_at END DESC
LIMIT COALESCE($10::INTEGER, 20)
OFFSET COALESCE($9::INTEGER, 0)
`

type ListCompanyInfoChangeRequestsWithFiltersParams struct {
	CompanyID     *int32               `json:"companyId"`
	Status        NullInfoChangeStatus `json:"status"`
	CreatedAfter  pgtype.Timestamptz   `json:"createdAfter"`
	CreatedBefore pgtype.Timestamptz   `json:"createdBefore"`
	ReviewedBy    *int32               `json:"reviewedBy"`
	SearchTerm    *string              `json:"searchTerm"`
	SortBy        *string              `json:"sortBy"`
	SortDir       *string              `json:"sortDir"`
	Offset        *int32               `json:"offset"`
	Limit         *int32               `json:"limit"`
}

type ListCompanyInfoChangeRequestsWithFiltersRow struct {
	ID           uint32           `json:"id"`
	CompanyID    uint32           `json:"companyId"`
	CompanyName  *string          `json:"companyName"`
	Status       InfoChangeStatus `json:"status"`
	CreatedAt    time.Time        `json:"createdAt"`
	ReviewedAt   time.Time        `json:"reviewedAt"`
	ReviewedBy   uint32           `json:"reviewedBy"`
	OriginalData []byte           `json:"originalData"`
	NewData      []byte           `json:"newData"`
	Remark       *string          `json:"remark"`
	ReviewRemark *string          `json:"reviewRemark"`
}

// 簡化動態排序邏輯
// 改進分頁參數處理，設置合理的默認值
func (q *Queries) ListCompanyInfoChangeRequestsWithFilters(ctx context.Context, arg ListCompanyInfoChangeRequestsWithFiltersParams) ([]*ListCompanyInfoChangeRequestsWithFiltersRow, error) {
	rows, err := q.db.Query(ctx, listCompanyInfoChangeRequestsWithFilters,
		arg.CompanyID,
		arg.Status,
		arg.CreatedAfter,
		arg.CreatedBefore,
		arg.ReviewedBy,
		arg.SearchTerm,
		arg.SortBy,
		arg.SortDir,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ListCompanyInfoChangeRequestsWithFiltersRow{}
	for rows.Next() {
		var i ListCompanyInfoChangeRequestsWithFiltersRow
		if err := rows.Scan(
			&i.ID,
			&i.CompanyID,
			&i.CompanyName,
			&i.Status,
			&i.CreatedAt,
			&i.ReviewedAt,
			&i.ReviewedBy,
			&i.OriginalData,
			&i.NewData,
			&i.Remark,
			&i.ReviewRemark,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPendingCompanyInfoChangeRequests = `-- name: ListPendingCompanyInfoChangeRequests :many
/*
    獲取所有待審核的廠商資料異動申請，按建立時間升序排序（先進先出原則）
    - 索引建議: 為status欄位創建索引，大幅提高查詢效率
    - 用途: 管理員審核頁面使用，按先後順序顯示待處理項目
    - 效能優化: 僅選擇必要欄位，減少數據傳輸量
*/
SELECT 
    id, 
    company_id, 
    original_data, 
    new_data, 
    status, 
    remark, 
    created_by,
    created_at
FROM company_info_change_requests
WHERE status = '待審'
ORDER BY created_at ASC
LIMIT 100
`

type ListPendingCompanyInfoChangeRequestsRow struct {
	ID           uint32           `json:"id"`
	CompanyID    uint32           `json:"companyId"`
	OriginalData []byte           `json:"originalData"`
	NewData      []byte           `json:"newData"`
	Status       InfoChangeStatus `json:"status"`
	Remark       *string          `json:"remark"`
	CreatedBy    uint32           `json:"createdBy"`
	CreatedAt    time.Time        `json:"createdAt"`
}

func (q *Queries) ListPendingCompanyInfoChangeRequests(ctx context.Context) ([]*ListPendingCompanyInfoChangeRequestsRow, error) {
	rows, err := q.db.Query(ctx, listPendingCompanyInfoChangeRequests)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ListPendingCompanyInfoChangeRequestsRow{}
	for rows.Next() {
		var i ListPendingCompanyInfoChangeRequestsRow
		if err := rows.Scan(
			&i.ID,
			&i.CompanyID,
			&i.OriginalData,
			&i.NewData,
			&i.Status,
			&i.Remark,
			&i.CreatedBy,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateCompanyInfoChangeRequestStatus = `-- name: UpdateCompanyInfoChangeRequestStatus :one
/*
    更新廠商資料異動申請的審核狀態
    - 使用場景: 管理員審核廠商資料異動申請
    - 業務規則: 
      * 狀態可改為'通過'或'退件'
      * 審核通過後可能需要觸發後續的廠商資料更新操作
    - 效能優化: 僅返回必要欄位，添加狀態檢查以確保只能更新待審申請
*/
UPDATE company_info_change_requests
SET
    status = $2,
    review_remark = $3,
    reviewed_by = $4,
    reviewed_at = CURRENT_TIMESTAMP,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
  AND status = '待審'  -- 確保只能更新待審狀態的申請
RETURNING 
    id, 
    company_id, 
    status, 
    review_remark, 
    reviewed_by, 
    reviewed_at
`

type UpdateCompanyInfoChangeRequestStatusParams struct {
	ID           uint32           `json:"id"`
	Status       InfoChangeStatus `json:"status"`
	ReviewRemark *string          `json:"reviewRemark"`
	ReviewedBy   uint32           `json:"reviewedBy"`
}

type UpdateCompanyInfoChangeRequestStatusRow struct {
	ID           uint32           `json:"id"`
	CompanyID    uint32           `json:"companyId"`
	Status       InfoChangeStatus `json:"status"`
	ReviewRemark *string          `json:"reviewRemark"`
	ReviewedBy   uint32           `json:"reviewedBy"`
	ReviewedAt   time.Time        `json:"reviewedAt"`
}

func (q *Queries) UpdateCompanyInfoChangeRequestStatus(ctx context.Context, arg UpdateCompanyInfoChangeRequestStatusParams) (*UpdateCompanyInfoChangeRequestStatusRow, error) {
	row := q.db.QueryRow(ctx, updateCompanyInfoChangeRequestStatus,
		arg.ID,
		arg.Status,
		arg.ReviewRemark,
		arg.ReviewedBy,
	)
	var i UpdateCompanyInfoChangeRequestStatusRow
	err := row.Scan(
		&i.ID,
		&i.CompanyID,
		&i.Status,
		&i.ReviewRemark,
		&i.ReviewedBy,
		&i.ReviewedAt,
	)
	return &i, err
}
