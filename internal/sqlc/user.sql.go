// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: user.sql

package sqlc

import (
	"context"
	"time"
)

const countUsers = `-- name: CountUsers :one
SELECT COUNT(id)
FROM users
WHERE
  -- 優化過濾條件，與 ListUsers 查詢保持一致的條件格式
    ($1::text = '' OR user_role = $1::user_role) AND
    ($2::text = '' OR status = $2::user_status) AND
    ($3::int = 0 OR company_id = $3::int) AND
    ($4::text = '' OR username ILIKE '%' || $4 || '%')
`

type CountUsersParams struct {
	UserRole   string `json:"userRole"`
	Status     string `json:"status"`
	CompanyID  int32  `json:"companyId"`
	SearchTerm string `json:"searchTerm"`
}

func (q *Queries) CountUsers(ctx context.Context, arg CountUsersParams) (int64, error) {
	row := q.db.QueryRow(ctx, countUsers,
		arg.UserRole,
		arg.Status,
		arg.CompanyID,
		arg.SearchTerm,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createUser = `-- name: CreateUser :one
INSERT INTO users (
    username,
    email,
    backup_email,
    password_hash,
    job_title,
    company_id,
    user_role,
    mobile,
    status
) VALUES (
             $1, $2, $3, $4, $5, $6, $7, $8, $9
         )
RETURNING id, username, email, backup_email, password_hash, job_title, company_id, user_role, status, created_at, updated_at
`

type CreateUserParams struct {
	Username     string     `json:"username"`
	Email        string     `json:"email"`
	BackupEmail  *string    `json:"backupEmail"`
	PasswordHash string     `json:"passwordHash"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	UserRole     UserRole   `json:"userRole"`
	Mobile       *string    `json:"mobile"`
	Status       UserStatus `json:"status"`
}

type CreateUserRow struct {
	ID           uint32     `json:"id"`
	Username     string     `json:"username"`
	Email        string     `json:"email"`
	BackupEmail  *string    `json:"backupEmail"`
	PasswordHash string     `json:"passwordHash"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	UserRole     UserRole   `json:"userRole"`
	Status       UserStatus `json:"status"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (*CreateUserRow, error) {
	row := q.db.QueryRow(ctx, createUser,
		arg.Username,
		arg.Email,
		arg.BackupEmail,
		arg.PasswordHash,
		arg.JobTitle,
		arg.CompanyID,
		arg.UserRole,
		arg.Mobile,
		arg.Status,
	)
	var i CreateUserRow
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.Email,
		&i.BackupEmail,
		&i.PasswordHash,
		&i.JobTitle,
		&i.CompanyID,
		&i.UserRole,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getUserByEmail = `-- name: GetUserByEmail :one
SELECT
    id,
    username,
    password_hash,
    email,
    backup_email,
    user_role,
    job_title,
    company_id,
    mobile,
    status,
    created_at,
    updated_at,
    last_login_at,
    password_expiration_at
FROM users
WHERE email = $1
LIMIT 1
`

func (q *Queries) GetUserByEmail(ctx context.Context, email string) (*User, error) {
	row := q.db.QueryRow(ctx, getUserByEmail, email)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.PasswordHash,
		&i.Email,
		&i.BackupEmail,
		&i.UserRole,
		&i.JobTitle,
		&i.CompanyID,
		&i.Mobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastLoginAt,
		&i.PasswordExpirationAt,
	)
	return &i, err
}

const getUserByID = `-- name: GetUserByID :one
SELECT
    id,
    username,
    password_hash,
    email,
    backup_email,
    user_role,
    job_title,
    company_id,
    mobile,
    status,
    created_at,
    updated_at,
    last_login_at
FROM users
WHERE id = $1
LIMIT 1
`

type GetUserByIDRow struct {
	ID           uint32     `json:"id"`
	Username     string     `json:"username"`
	PasswordHash string     `json:"passwordHash"`
	Email        string     `json:"email"`
	BackupEmail  *string    `json:"backupEmail"`
	UserRole     UserRole   `json:"userRole"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	Mobile       *string    `json:"mobile"`
	Status       UserStatus `json:"status"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	LastLoginAt  time.Time  `json:"lastLoginAt"`
}

func (q *Queries) GetUserByID(ctx context.Context, id uint32) (*GetUserByIDRow, error) {
	row := q.db.QueryRow(ctx, getUserByID, id)
	var i GetUserByIDRow
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.PasswordHash,
		&i.Email,
		&i.BackupEmail,
		&i.UserRole,
		&i.JobTitle,
		&i.CompanyID,
		&i.Mobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastLoginAt,
	)
	return &i, err
}

const getUserByUsername = `-- name: GetUserByUsername :one
SELECT
    id,
    username,
    password_hash,
    email,
    backup_email,
    user_role,
    job_title,
    company_id,
    mobile,
    status,
    created_at,
    updated_at,
    last_login_at
FROM users
WHERE username = $1
LIMIT 1
`

type GetUserByUsernameRow struct {
	ID           uint32     `json:"id"`
	Username     string     `json:"username"`
	PasswordHash string     `json:"passwordHash"`
	Email        string     `json:"email"`
	BackupEmail  *string    `json:"backupEmail"`
	UserRole     UserRole   `json:"userRole"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	Mobile       *string    `json:"mobile"`
	Status       UserStatus `json:"status"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	LastLoginAt  time.Time  `json:"lastLoginAt"`
}

func (q *Queries) GetUserByUsername(ctx context.Context, username string) (*GetUserByUsernameRow, error) {
	row := q.db.QueryRow(ctx, getUserByUsername, username)
	var i GetUserByUsernameRow
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.PasswordHash,
		&i.Email,
		&i.BackupEmail,
		&i.UserRole,
		&i.JobTitle,
		&i.CompanyID,
		&i.Mobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastLoginAt,
	)
	return &i, err
}

const getUsersByIDs = `-- name: GetUsersByIDs :many
SELECT
    id,
    username,
    password_hash,
    email,
    backup_email,
    user_role,
    job_title,
    company_id,
    mobile,
    status,
    created_at,
    updated_at,
    last_login_at
FROM users
WHERE id = ANY($1::integer[])
`

type GetUsersByIDsRow struct {
	ID           uint32     `json:"id"`
	Username     string     `json:"username"`
	PasswordHash string     `json:"passwordHash"`
	Email        string     `json:"email"`
	BackupEmail  *string    `json:"backupEmail"`
	UserRole     UserRole   `json:"userRole"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	Mobile       *string    `json:"mobile"`
	Status       UserStatus `json:"status"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	LastLoginAt  time.Time  `json:"lastLoginAt"`
}

func (q *Queries) GetUsersByIDs(ctx context.Context, ids []int32) ([]*GetUsersByIDsRow, error) {
	rows, err := q.db.Query(ctx, getUsersByIDs, ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetUsersByIDsRow{}
	for rows.Next() {
		var i GetUsersByIDsRow
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.PasswordHash,
			&i.Email,
			&i.BackupEmail,
			&i.UserRole,
			&i.JobTitle,
			&i.CompanyID,
			&i.Mobile,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LastLoginAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUsers = `-- name: ListUsers :many
SELECT
    id,
    username,
    password_hash,
    email,
    backup_email,
    user_role,
    job_title,
    company_id,
    mobile,
    status,
    created_at,
    updated_at,
    last_login_at
FROM users
WHERE
  -- 優化過濾條件，避免在每行都需要檢查條件
    ($1::text = '' OR user_role = $1::user_role) AND
    ($2::text = '' OR status = $2::user_status) AND
    ($3::int = 0 OR company_id = $3::int) AND
    ($4::text = '' OR username ILIKE '%' || $4 || '%')
ORDER BY created_at ASC
LIMIT CASE
          WHEN $6::int > 0 THEN $6::int
          WHEN $6 IS NULL THEN NULL
          ELSE 2147483647 -- PostgreSQL 的最大整數，實際上相當於無限制
    END
    OFFSET $5::int
`

type ListUsersParams struct {
	UserRole   string `json:"userRole"`
	Status     string `json:"status"`
	CompanyID  int32  `json:"companyId"`
	SearchTerm string `json:"searchTerm"`
	OffsetVal  int32  `json:"offsetVal"`
	LimitVal   int32  `json:"limitVal"`
}

type ListUsersRow struct {
	ID           uint32     `json:"id"`
	Username     string     `json:"username"`
	PasswordHash string     `json:"passwordHash"`
	Email        string     `json:"email"`
	BackupEmail  *string    `json:"backupEmail"`
	UserRole     UserRole   `json:"userRole"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	Mobile       *string    `json:"mobile"`
	Status       UserStatus `json:"status"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	LastLoginAt  time.Time  `json:"lastLoginAt"`
}

func (q *Queries) ListUsers(ctx context.Context, arg ListUsersParams) ([]*ListUsersRow, error) {
	rows, err := q.db.Query(ctx, listUsers,
		arg.UserRole,
		arg.Status,
		arg.CompanyID,
		arg.SearchTerm,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ListUsersRow{}
	for rows.Next() {
		var i ListUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.PasswordHash,
			&i.Email,
			&i.BackupEmail,
			&i.UserRole,
			&i.JobTitle,
			&i.CompanyID,
			&i.Mobile,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LastLoginAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUsersByCompanyID = `-- name: ListUsersByCompanyID :many
SELECT
    id,
    username,
    password_hash,
    email,
    backup_email,
    user_role,
    job_title,
    company_id,
    mobile,
    status,
    created_at,
    updated_at,
    last_login_at
FROM users
WHERE company_id = $1
ORDER BY created_at ASC
`

type ListUsersByCompanyIDRow struct {
	ID           uint32     `json:"id"`
	Username     string     `json:"username"`
	PasswordHash string     `json:"passwordHash"`
	Email        string     `json:"email"`
	BackupEmail  *string    `json:"backupEmail"`
	UserRole     UserRole   `json:"userRole"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	Mobile       *string    `json:"mobile"`
	Status       UserStatus `json:"status"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	LastLoginAt  time.Time  `json:"lastLoginAt"`
}

func (q *Queries) ListUsersByCompanyID(ctx context.Context, companyID uint32) ([]*ListUsersByCompanyIDRow, error) {
	rows, err := q.db.Query(ctx, listUsersByCompanyID, companyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ListUsersByCompanyIDRow{}
	for rows.Next() {
		var i ListUsersByCompanyIDRow
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.PasswordHash,
			&i.Email,
			&i.BackupEmail,
			&i.UserRole,
			&i.JobTitle,
			&i.CompanyID,
			&i.Mobile,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LastLoginAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUsersByStatuses = `-- name: ListUsersByStatuses :many
SELECT
    id,
    username,
    password_hash,
    email,
    backup_email,
    user_role,
    job_title,
    company_id,
    mobile,
    status,
    created_at,
    updated_at,
    last_login_at
FROM users
WHERE status = ANY($1::user_status[])
`

type ListUsersByStatusesRow struct {
	ID           uint32     `json:"id"`
	Username     string     `json:"username"`
	PasswordHash string     `json:"passwordHash"`
	Email        string     `json:"email"`
	BackupEmail  *string    `json:"backupEmail"`
	UserRole     UserRole   `json:"userRole"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	Mobile       *string    `json:"mobile"`
	Status       UserStatus `json:"status"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	LastLoginAt  time.Time  `json:"lastLoginAt"`
}

func (q *Queries) ListUsersByStatuses(ctx context.Context, statuses []UserStatus) ([]*ListUsersByStatusesRow, error) {
	rows, err := q.db.Query(ctx, listUsersByStatuses, statuses)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ListUsersByStatusesRow{}
	for rows.Next() {
		var i ListUsersByStatusesRow
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.PasswordHash,
			&i.Email,
			&i.BackupEmail,
			&i.UserRole,
			&i.JobTitle,
			&i.CompanyID,
			&i.Mobile,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LastLoginAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateLastLogin = `-- name: UpdateLastLogin :one
UPDATE users
SET
    last_login_at = CURRENT_TIMESTAMP,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, username, email, password_hash, job_title, company_id, user_role, status, created_at, updated_at, last_login_at
`

type UpdateLastLoginRow struct {
	ID           uint32     `json:"id"`
	Username     string     `json:"username"`
	Email        string     `json:"email"`
	PasswordHash string     `json:"passwordHash"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	UserRole     UserRole   `json:"userRole"`
	Status       UserStatus `json:"status"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	LastLoginAt  time.Time  `json:"lastLoginAt"`
}

func (q *Queries) UpdateLastLogin(ctx context.Context, id uint32) (*UpdateLastLoginRow, error) {
	row := q.db.QueryRow(ctx, updateLastLogin, id)
	var i UpdateLastLoginRow
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.Email,
		&i.PasswordHash,
		&i.JobTitle,
		&i.CompanyID,
		&i.UserRole,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastLoginAt,
	)
	return &i, err
}

const updatePassword = `-- name: UpdatePassword :one
UPDATE users
SET
    password_hash = $2,
    password_expiration_at = CURRENT_TIMESTAMP + INTERVAL '90 days',
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, username, email, password_hash, job_title, company_id, user_role, status, created_at, updated_at, last_login_at
`

type UpdatePasswordParams struct {
	ID           uint32 `json:"id"`
	PasswordHash string `json:"passwordHash"`
}

type UpdatePasswordRow struct {
	ID           uint32     `json:"id"`
	Username     string     `json:"username"`
	Email        string     `json:"email"`
	PasswordHash string     `json:"passwordHash"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	UserRole     UserRole   `json:"userRole"`
	Status       UserStatus `json:"status"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	LastLoginAt  time.Time  `json:"lastLoginAt"`
}

func (q *Queries) UpdatePassword(ctx context.Context, arg UpdatePasswordParams) (*UpdatePasswordRow, error) {
	row := q.db.QueryRow(ctx, updatePassword, arg.ID, arg.PasswordHash)
	var i UpdatePasswordRow
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.Email,
		&i.PasswordHash,
		&i.JobTitle,
		&i.CompanyID,
		&i.UserRole,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastLoginAt,
	)
	return &i, err
}

const updateUser = `-- name: UpdateUser :one
UPDATE users
SET
    username = COALESCE($2, username),
    password_hash = COALESCE($3, password_hash),
    job_title = COALESCE($4, job_title),
    company_id = COALESCE($5::int, company_id),
    user_role = COALESCE($6::user_role, user_role),
    status = COALESCE($7::user_status, status),
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, username, email, password_hash, job_title, company_id, user_role, status, created_at, updated_at, last_login_at
`

type UpdateUserParams struct {
	ID           uint32         `json:"id"`
	Username     *string        `json:"username"`
	PasswordHash *string        `json:"passwordHash"`
	JobTitle     *string        `json:"jobTitle"`
	CompanyID    *int32         `json:"companyId"`
	UserRole     NullUserRole   `json:"userRole"`
	Status       NullUserStatus `json:"status"`
}

type UpdateUserRow struct {
	ID           uint32     `json:"id"`
	Username     string     `json:"username"`
	Email        string     `json:"email"`
	PasswordHash string     `json:"passwordHash"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	UserRole     UserRole   `json:"userRole"`
	Status       UserStatus `json:"status"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	LastLoginAt  time.Time  `json:"lastLoginAt"`
}

func (q *Queries) UpdateUser(ctx context.Context, arg UpdateUserParams) (*UpdateUserRow, error) {
	row := q.db.QueryRow(ctx, updateUser,
		arg.ID,
		arg.Username,
		arg.PasswordHash,
		arg.JobTitle,
		arg.CompanyID,
		arg.UserRole,
		arg.Status,
	)
	var i UpdateUserRow
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.Email,
		&i.PasswordHash,
		&i.JobTitle,
		&i.CompanyID,
		&i.UserRole,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastLoginAt,
	)
	return &i, err
}

const updateUserCompany = `-- name: UpdateUserCompany :one
UPDATE users
SET
    company_id = $2,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, username, email, job_title, company_id, user_role, status, updated_at
`

type UpdateUserCompanyParams struct {
	ID        uint32 `json:"id"`
	CompanyID uint32 `json:"companyId"`
}

type UpdateUserCompanyRow struct {
	ID        uint32     `json:"id"`
	Username  string     `json:"username"`
	Email     string     `json:"email"`
	JobTitle  *string    `json:"jobTitle"`
	CompanyID uint32     `json:"companyId"`
	UserRole  UserRole   `json:"userRole"`
	Status    UserStatus `json:"status"`
	UpdatedAt time.Time  `json:"updatedAt"`
}

func (q *Queries) UpdateUserCompany(ctx context.Context, arg UpdateUserCompanyParams) (*UpdateUserCompanyRow, error) {
	row := q.db.QueryRow(ctx, updateUserCompany, arg.ID, arg.CompanyID)
	var i UpdateUserCompanyRow
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.Email,
		&i.JobTitle,
		&i.CompanyID,
		&i.UserRole,
		&i.Status,
		&i.UpdatedAt,
	)
	return &i, err
}

const updateUserStatus = `-- name: UpdateUserStatus :one
UPDATE users
SET
    status = $2::user_status,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, username, email, password_hash, job_title, company_id, user_role, status, created_at, updated_at, last_login_at
`

type UpdateUserStatusParams struct {
	ID      uint32     `json:"id"`
	Column2 UserStatus `json:"column2"`
}

type UpdateUserStatusRow struct {
	ID           uint32     `json:"id"`
	Username     string     `json:"username"`
	Email        string     `json:"email"`
	PasswordHash string     `json:"passwordHash"`
	JobTitle     *string    `json:"jobTitle"`
	CompanyID    uint32     `json:"companyId"`
	UserRole     UserRole   `json:"userRole"`
	Status       UserStatus `json:"status"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	LastLoginAt  time.Time  `json:"lastLoginAt"`
}

func (q *Queries) UpdateUserStatus(ctx context.Context, arg UpdateUserStatusParams) (*UpdateUserStatusRow, error) {
	row := q.db.QueryRow(ctx, updateUserStatus, arg.ID, arg.Column2)
	var i UpdateUserStatusRow
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.Email,
		&i.PasswordHash,
		&i.JobTitle,
		&i.CompanyID,
		&i.UserRole,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastLoginAt,
	)
	return &i, err
}
