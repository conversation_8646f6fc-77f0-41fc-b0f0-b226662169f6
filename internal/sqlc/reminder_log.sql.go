// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: reminder_log.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

type BatchCreateReminderLogsParams struct {
	ScheduleID         uint32          `json:"scheduleId"`
	CompanyID          uint32          `json:"companyId"`
	Email              string          `json:"email"`
	BackupEmail        *string         `json:"backupEmail"`
	Status             EmailStatus     `json:"status"`
	BackupStatus       NullEmailStatus `json:"backupStatus"`
	ErrorMessage       *string         `json:"errorMessage"`
	BackupErrorMessage *string         `json:"backupErrorMessage"`
}

const createReminderLog = `-- name: CreateReminderLog :one
INSERT INTO reminder_logs (
    schedule_id,
    company_id,
    email,
    backup_email,
    status,
    backup_status,
    error_message,
    backup_error_message
) VALUES (
             $1, $2, $3, $4, $5, $6, $7, $8
         )
RETURNING
    id, schedule_id, company_id, email, backup_email, status,
    backup_status, error_message, backup_error_message, sent_at
`

type CreateReminderLogParams struct {
	ScheduleID         uint32          `json:"scheduleId"`
	CompanyID          uint32          `json:"companyId"`
	Email              string          `json:"email"`
	BackupEmail        *string         `json:"backupEmail"`
	Status             EmailStatus     `json:"status"`
	BackupStatus       NullEmailStatus `json:"backupStatus"`
	ErrorMessage       *string         `json:"errorMessage"`
	BackupErrorMessage *string         `json:"backupErrorMessage"`
}

func (q *Queries) CreateReminderLog(ctx context.Context, arg CreateReminderLogParams) (*ReminderLog, error) {
	row := q.db.QueryRow(ctx, createReminderLog,
		arg.ScheduleID,
		arg.CompanyID,
		arg.Email,
		arg.BackupEmail,
		arg.Status,
		arg.BackupStatus,
		arg.ErrorMessage,
		arg.BackupErrorMessage,
	)
	var i ReminderLog
	err := row.Scan(
		&i.ID,
		&i.ScheduleID,
		&i.CompanyID,
		&i.Email,
		&i.BackupEmail,
		&i.Status,
		&i.BackupStatus,
		&i.ErrorMessage,
		&i.BackupErrorMessage,
		&i.SentAt,
	)
	return &i, err
}

const listReminderLogsByCompanyID = `-- name: ListReminderLogsByCompanyID :many
SELECT
    rl.id,
    rl.schedule_id,
    rl.company_id,
    rl.email,
    rl.backup_email,
    rl.status,
    rl.backup_status,
    rl.error_message,
    rl.backup_error_message,
    rl.sent_at,
    rs.project_id,
    rs.target_type,
    rs.email_subject,
    rs.start_date
FROM reminder_logs rl
         JOIN reminder_schedules rs ON rl.schedule_id = rs.id
WHERE rl.company_id = $1
ORDER BY rl.sent_at DESC
`

type ListReminderLogsByCompanyIDRow struct {
	ID                 uint32           `json:"id"`
	ScheduleID         uint32           `json:"scheduleId"`
	CompanyID          uint32           `json:"companyId"`
	Email              string           `json:"email"`
	BackupEmail        *string          `json:"backupEmail"`
	Status             EmailStatus      `json:"status"`
	BackupStatus       NullEmailStatus  `json:"backupStatus"`
	ErrorMessage       *string          `json:"errorMessage"`
	BackupErrorMessage *string          `json:"backupErrorMessage"`
	SentAt             pgtype.Timestamp `json:"sentAt"`
	ProjectID          uint32           `json:"projectId"`
	TargetType         ReminderTarget   `json:"targetType"`
	EmailSubject       string           `json:"emailSubject"`
	StartDate          pgtype.Timestamp `json:"startDate"`
}

func (q *Queries) ListReminderLogsByCompanyID(ctx context.Context, companyID uint32) ([]*ListReminderLogsByCompanyIDRow, error) {
	rows, err := q.db.Query(ctx, listReminderLogsByCompanyID, companyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ListReminderLogsByCompanyIDRow{}
	for rows.Next() {
		var i ListReminderLogsByCompanyIDRow
		if err := rows.Scan(
			&i.ID,
			&i.ScheduleID,
			&i.CompanyID,
			&i.Email,
			&i.BackupEmail,
			&i.Status,
			&i.BackupStatus,
			&i.ErrorMessage,
			&i.BackupErrorMessage,
			&i.SentAt,
			&i.ProjectID,
			&i.TargetType,
			&i.EmailSubject,
			&i.StartDate,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listReminderLogsByScheduleID = `-- name: ListReminderLogsByScheduleID :many
SELECT
    id,
    schedule_id,
    company_id,
    email,
    backup_email,
    status,
    backup_status,
    error_message,
    backup_error_message,
    sent_at
FROM reminder_logs
WHERE schedule_id = $1
ORDER BY sent_at DESC
`

func (q *Queries) ListReminderLogsByScheduleID(ctx context.Context, scheduleID uint32) ([]*ReminderLog, error) {
	rows, err := q.db.Query(ctx, listReminderLogsByScheduleID, scheduleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ReminderLog{}
	for rows.Next() {
		var i ReminderLog
		if err := rows.Scan(
			&i.ID,
			&i.ScheduleID,
			&i.CompanyID,
			&i.Email,
			&i.BackupEmail,
			&i.Status,
			&i.BackupStatus,
			&i.ErrorMessage,
			&i.BackupErrorMessage,
			&i.SentAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
