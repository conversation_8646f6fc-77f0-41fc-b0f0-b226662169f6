// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: system_log.sql

package sqlc

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const countSystemLogs = `-- name: CountSystemLogs :one
SELECT COUNT(id)
FROM system_logs
WHERE
    CASE WHEN $1::bigint > 0 THEN user_id = $1 ELSE TRUE END AND
    CASE WHEN $2::bigint > 0 THEN project_id = $2 ELSE TRUE END AND
    CASE WHEN $3::text != '' THEN unified_business_no = $3 ELSE TRUE END AND
    CASE WHEN $4 != '' THEN log_type = $4::system_log_type ELSE TRUE END AND
    CASE WHEN $5::text != '' THEN ip_address = $5 ELSE TRUE END AND
    CASE WHEN $6::timestamp IS NOT NULL THEN created_at >= $6 ELSE TRUE END AND
    CASE WHEN $7::timestamp IS NOT NULL THEN created_at <= $7 ELSE TRUE END AND
    CASE WHEN $8::text != '' THEN
             message ILIKE '%' || $8 || '%' OR
             unified_business_no ILIKE '%' || $8 || '%' OR
             log_type::text ILIKE '%' || $8 || '%'
         ELSE TRUE END
`

type CountSystemLogsParams struct {
	UserID            int64            `json:"userId"`
	ProjectID         int64            `json:"projectId"`
	UnifiedBusinessNo string           `json:"unifiedBusinessNo"`
	LogType           *string          `json:"logType"`
	IpAddress         string           `json:"ipAddress"`
	FromDate          pgtype.Timestamp `json:"fromDate"`
	ToDate            pgtype.Timestamp `json:"toDate"`
	SearchTerm        string           `json:"searchTerm"`
}

func (q *Queries) CountSystemLogs(ctx context.Context, arg CountSystemLogsParams) (int64, error) {
	row := q.db.QueryRow(ctx, countSystemLogs,
		arg.UserID,
		arg.ProjectID,
		arg.UnifiedBusinessNo,
		arg.LogType,
		arg.IpAddress,
		arg.FromDate,
		arg.ToDate,
		arg.SearchTerm,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createSystemLog = `-- name: CreateSystemLog :one
INSERT INTO system_logs (
    user_id,
    unified_business_no,
    log_type,
    message,
    project_id,
    ip_address
) VALUES (
             $1, $2, $3, $4, $5,$6
         )
RETURNING
    id, user_id, unified_business_no, log_type, message, ip_address, created_at
`

type CreateSystemLogParams struct {
	UserID            uint32            `json:"userId"`
	UnifiedBusinessNo *string           `json:"unifiedBusinessNo"`
	LogType           NullSystemLogType `json:"logType"`
	Message           *string           `json:"message"`
	ProjectID         uint32            `json:"projectId"`
	IpAddress         *string           `json:"ipAddress"`
}

type CreateSystemLogRow struct {
	ID                uint32            `json:"id"`
	UserID            uint32            `json:"userId"`
	UnifiedBusinessNo *string           `json:"unifiedBusinessNo"`
	LogType           NullSystemLogType `json:"logType"`
	Message           *string           `json:"message"`
	IpAddress         *string           `json:"ipAddress"`
	CreatedAt         time.Time         `json:"createdAt"`
}

func (q *Queries) CreateSystemLog(ctx context.Context, arg CreateSystemLogParams) (*CreateSystemLogRow, error) {
	row := q.db.QueryRow(ctx, createSystemLog,
		arg.UserID,
		arg.UnifiedBusinessNo,
		arg.LogType,
		arg.Message,
		arg.ProjectID,
		arg.IpAddress,
	)
	var i CreateSystemLogRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.UnifiedBusinessNo,
		&i.LogType,
		&i.Message,
		&i.IpAddress,
		&i.CreatedAt,
	)
	return &i, err
}

const listSystemLogs = `-- name: ListSystemLogs :many
SELECT
    id,
    user_id,
    unified_business_no,
    log_type,
    message,
    project_id,
    ip_address,
    created_at
FROM system_logs
WHERE
    CASE WHEN $1::bigint > 0 THEN user_id = $1 ELSE TRUE END AND
    CASE WHEN $2::bigint > 0 THEN project_id = $2 ELSE TRUE END AND
    CASE WHEN $3::text != '' THEN unified_business_no = $3 ELSE TRUE END AND
    CASE WHEN $4 != '' THEN log_type = $4::system_log_type ELSE TRUE END AND
    CASE WHEN $5::text != '' THEN ip_address = $5 ELSE TRUE END AND
    CASE WHEN $6::timestamp IS NOT NULL THEN created_at >= $6 ELSE TRUE END AND
    CASE WHEN $7::timestamp IS NOT NULL THEN created_at <= $7 ELSE TRUE END AND
    CASE WHEN $8::text != '' THEN
             message ILIKE '%' || $8 || '%' OR
             unified_business_no ILIKE '%' || $8 || '%' OR
             log_type::text ILIKE '%' || $8 || '%'
         ELSE TRUE END
ORDER BY
    CASE WHEN $9::text = 'asc' THEN created_at END ASC,
    CASE WHEN $9::text = 'desc' OR $9::text = '' THEN created_at END DESC
LIMIT CASE
          WHEN $11::int > 0 THEN $11::int
          WHEN $11 IS NULL THEN NULL
          ELSE 2147483647 -- PostgreSQL 的最大整數，實際上相當於無限制
    END
    OFFSET $10::int
`

type ListSystemLogsParams struct {
	UserID            int64            `json:"userId"`
	ProjectID         int64            `json:"projectId"`
	UnifiedBusinessNo string           `json:"unifiedBusinessNo"`
	LogType           *string          `json:"logType"`
	IpAddress         string           `json:"ipAddress"`
	FromDate          pgtype.Timestamp `json:"fromDate"`
	ToDate            pgtype.Timestamp `json:"toDate"`
	SearchTerm        string           `json:"searchTerm"`
	SortDir           string           `json:"sortDir"`
	OffsetVal         int32            `json:"offsetVal"`
	LimitVal          int32            `json:"limitVal"`
}

func (q *Queries) ListSystemLogs(ctx context.Context, arg ListSystemLogsParams) ([]*SystemLog, error) {
	rows, err := q.db.Query(ctx, listSystemLogs,
		arg.UserID,
		arg.ProjectID,
		arg.UnifiedBusinessNo,
		arg.LogType,
		arg.IpAddress,
		arg.FromDate,
		arg.ToDate,
		arg.SearchTerm,
		arg.SortDir,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SystemLog{}
	for rows.Next() {
		var i SystemLog
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.UnifiedBusinessNo,
			&i.LogType,
			&i.Message,
			&i.ProjectID,
			&i.IpAddress,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
