// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: company_document.sql

package sqlc

import (
	"context"
)

const createCompanyDocument = `-- name: CreateCompanyDocument :one
INSERT INTO company_documents (
    company_id,
    document_type,
    file_path,
    file_name,
    file_size,
    file_type,
    file_hash,
    status
) VALUES (
             $1, $2, $3, $4, $5, $6, $7, $8
         )
RETURNING
    id, company_id, document_type, file_path, file_name, file_size,
    file_type, file_hash, uploaded_at, status, remark
`

type CreateCompanyDocumentParams struct {
	CompanyID    uint32         `json:"companyId"`
	DocumentType DocumentType   `json:"documentType"`
	FilePath     string         `json:"filePath"`
	FileName     string         `json:"fileName"`
	FileSize     int32          `json:"fileSize"`
	FileType     string         `json:"fileType"`
	FileHash     *string        `json:"fileHash"`
	Status       DocumentStatus `json:"status"`
}

func (q *Queries) CreateCompanyDocument(ctx context.Context, arg CreateCompanyDocumentParams) (*CompanyDocument, error) {
	row := q.db.QueryRow(ctx, createCompanyDocument,
		arg.CompanyID,
		arg.DocumentType,
		arg.FilePath,
		arg.FileName,
		arg.FileSize,
		arg.FileType,
		arg.FileHash,
		arg.Status,
	)
	var i CompanyDocument
	err := row.Scan(
		&i.ID,
		&i.CompanyID,
		&i.DocumentType,
		&i.FilePath,
		&i.FileName,
		&i.FileSize,
		&i.FileType,
		&i.FileHash,
		&i.UploadedAt,
		&i.Status,
		&i.Remark,
	)
	return &i, err
}

const deleteCompanyDocument = `-- name: DeleteCompanyDocument :exec
DELETE FROM company_documents
WHERE id = $1
`

func (q *Queries) DeleteCompanyDocument(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteCompanyDocument, id)
	return err
}

const getCompanyDocumentByID = `-- name: GetCompanyDocumentByID :one
SELECT
    id,
    company_id,
    document_type,
    file_path,
    file_name,
    file_size,
    file_type,
    file_hash,
    uploaded_at,
    status,
    remark
FROM company_documents
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetCompanyDocumentByID(ctx context.Context, id uint32) (*CompanyDocument, error) {
	row := q.db.QueryRow(ctx, getCompanyDocumentByID, id)
	var i CompanyDocument
	err := row.Scan(
		&i.ID,
		&i.CompanyID,
		&i.DocumentType,
		&i.FilePath,
		&i.FileName,
		&i.FileSize,
		&i.FileType,
		&i.FileHash,
		&i.UploadedAt,
		&i.Status,
		&i.Remark,
	)
	return &i, err
}

const listCompanyDocumentsByCompanyID = `-- name: ListCompanyDocumentsByCompanyID :many
SELECT
    id,
    company_id,
    document_type,
    file_path,
    file_name,
    file_size,
    file_type,
    file_hash,
    uploaded_at,
    status,
    remark
FROM company_documents
WHERE company_id = $1
ORDER BY uploaded_at DESC
`

func (q *Queries) ListCompanyDocumentsByCompanyID(ctx context.Context, companyID uint32) ([]*CompanyDocument, error) {
	rows, err := q.db.Query(ctx, listCompanyDocumentsByCompanyID, companyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CompanyDocument{}
	for rows.Next() {
		var i CompanyDocument
		if err := rows.Scan(
			&i.ID,
			&i.CompanyID,
			&i.DocumentType,
			&i.FilePath,
			&i.FileName,
			&i.FileSize,
			&i.FileType,
			&i.FileHash,
			&i.UploadedAt,
			&i.Status,
			&i.Remark,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateCompanyDocumentStatus = `-- name: UpdateCompanyDocumentStatus :one
UPDATE company_documents
SET
    status = $2::document_status,
    remark = $3
WHERE id = $1
RETURNING
    id, company_id, document_type, file_path, file_name, file_size,
    file_type, file_hash, uploaded_at, status, remark
`

type UpdateCompanyDocumentStatusParams struct {
	ID      uint32         `json:"id"`
	Column2 DocumentStatus `json:"column2"`
	Remark  *string        `json:"remark"`
}

func (q *Queries) UpdateCompanyDocumentStatus(ctx context.Context, arg UpdateCompanyDocumentStatusParams) (*CompanyDocument, error) {
	row := q.db.QueryRow(ctx, updateCompanyDocumentStatus, arg.ID, arg.Column2, arg.Remark)
	var i CompanyDocument
	err := row.Scan(
		&i.ID,
		&i.CompanyID,
		&i.DocumentType,
		&i.FilePath,
		&i.FileName,
		&i.FileSize,
		&i.FileType,
		&i.FileHash,
		&i.UploadedAt,
		&i.Status,
		&i.Remark,
	)
	return &i, err
}
