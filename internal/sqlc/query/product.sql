-- name: GetProductByID :one
SELECT
    id,
    pid,
    product_vat,
    product_company,
    group_id,
    group_name,
    project_id,
    item_id,
    brand,
    name,
    product_nation,
    unit_type,
    category,
    info,
    auth,
    price,
    price_invoice,
    bid_price,
    auth_pc,
    auth_svr,
    auth_cal,
    auth_mobile,
    auth_core,
    ship_auth,
    ship_box,
    ship_disk,
    memo,
    step_start,
    step_end,
    is_deleted,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM products
WHERE id = $1
LIMIT 1;

-- name: GetProductByGroupAndItemID :one
SELECT
    id,
    pid,
    product_vat,
    product_company,
    group_id,
    group_name,
    item_id,
    brand,
    name,
    product_nation,
    unit_type,
    category,
    info,
    auth,
    price,
    price_invoice,
    bid_price,
    auth_pc,
    auth_svr,
    auth_cal,
    auth_mobile,
    auth_core,
    ship_auth,
    ship_box,
    ship_disk,
    memo,
    step_start,
    step_end,
    is_deleted,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM products
WHERE group_id = $1 AND item_id = $2 AND is_deleted = FALSE
LIMIT 1;


-- name: ListProductsByGroupID :many
SELECT
    id,
    pid,
    product_vat,
    product_company,
    group_id,
    group_name,
    project_id,
    item_id,
    brand,
    name,
    product_nation,
    unit_type,
    category,
    info,
    auth,
    price,
    price_invoice,
    bid_price,
    auth_pc,
    auth_svr,
    auth_cal,
    auth_mobile,
    auth_core,
    ship_auth,
    ship_box,
    ship_disk,
    memo,
    step_start,
    step_end,
    is_deleted,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM products
WHERE
    group_id = $1 AND
    CASE WHEN @include_deleted::boolean = TRUE THEN TRUE ELSE is_deleted = FALSE END
ORDER BY item_id ASC;


-- name: ListProductsByProjectID :many
SELECT
    p.id,
    p.pid,
    p.product_vat,
    p.product_company,
    p.group_id,
    p.group_name,
    p.project_id,
    p.item_id,
    p.brand,
    p.name,
    p.product_nation,
    p.unit_type,
    p.category,
    p.info,
    p.auth,
    p.price,
    p.price_invoice,
    p.bid_price,
    p.auth_pc,
    p.auth_svr,
    p.auth_cal,
    p.auth_mobile,
    p.auth_core,
    p.ship_auth,
    p.ship_box,
    p.ship_disk,
    p.memo,
    p.step_start,
    p.step_end,
    p.is_deleted,
    p.created_at,
    p.updated_at,
    p.created_by,
    p.updated_by
FROM products p
WHERE
    p.project_id = $1 AND
    CASE WHEN @category::text != '' THEN p.category = @category::text ELSE TRUE END AND
    CASE WHEN @brand::text != '' THEN p.brand = @brand ELSE TRUE END AND
    CASE WHEN @search_term::text != '' THEN
             p.name ILIKE '%' || @search_term || '%' OR
             p.item_id::text ILIKE '%' || @search_term || '%' OR
             p.product_company ILIKE '%' || @search_term || '%'
         ELSE TRUE END AND
    CASE WHEN @include_deleted::boolean = TRUE THEN TRUE ELSE p.is_deleted = FALSE END
ORDER BY
    CASE WHEN @sort_by::text = 'item_id' AND @sort_dir::text = 'asc' THEN p.item_id END ASC,
    CASE WHEN @sort_by::text = 'item_id' AND @sort_dir::text = 'desc' THEN p.item_id END DESC,
    CASE WHEN @sort_by::text = 'name' AND @sort_dir::text = 'asc' THEN p.name END ASC,
    CASE WHEN @sort_by::text = 'name' AND @sort_dir::text = 'desc' THEN p.name END DESC,
    p.group_id ASC, p.item_id ASC
LIMIT CASE
          WHEN @limit_val::int > 0 THEN @limit_val::int
          WHEN @limit_val IS NULL THEN NULL
          ELSE 2147483647
    END
    OFFSET @offset_val::int;


-- 第一步：標記要刪除的重複產品
-- name: MarkDuplicatesForDeletion :many
-- 這個查詢會找出所有重複的產品，並準備刪除
WITH duplicate_products AS (
    SELECT DISTINCT
        p.id,
        p.pid,
        p.product_vat,
        p.group_id,
        p.item_id,
        p.name,
        p.category
    FROM products p
    WHERE (p.pid, p.product_vat, p.group_id, p.item_id, p.name) IN (
        -- 將要檢查的組合通過 VALUES 或參數數組傳入
        SELECT unnest(@pids::int[]), unnest(@product_vats::text[]),
               unnest(@group_ids::int[]), unnest(@item_ids::int[]),
               unnest(@names::text[])
    )
      AND p.is_deleted = false
)
SELECT
    id,
    pid,
    product_vat,
    group_id,
    item_id,
    name,
    category
FROM duplicate_products;

-- name: DeleteDuplicates :exec
-- 使用硬刪除，保持資料安全
DELETE FROM products
WHERE id = ANY($1::bigint[]);

-- 第四步：插入新資料
-- name: BatchInsertAllProducts :copyfrom
INSERT INTO products (
    pid,
    product_vat,
    product_company,
    group_id,
    group_name,
    project_id,
    item_id,
    brand,
    name,
    product_nation,
    unit_type,
    category,
    info,
    auth,
    price,
    price_invoice,
    bid_price,
    auth_pc,
    auth_svr,
    auth_cal,
    auth_mobile,
    auth_core,
    ship_auth,
    ship_box,
    ship_disk,
    memo,
    step_start,
    step_end,
    created_by,
    updated_by
) VALUES (
             $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
             $11, $12, $13, $14, $15, $16, $17, $18, $19, $20,
             $21, $22, $23, $24, $25, $26, $27, $28, $29, $30
         );


-- name: FindAndHardDeleteDuplicatesForImport :many
WITH input_params AS (
    SELECT
        unnest(@pids::int[]) as pid,
        unnest(@product_vats::text[]) as product_vat,
        unnest(@group_ids::int[]) as group_id,
        unnest(@item_ids::int[]) as item_id,
        unnest(@names::text[]) as name
),
-- 使用 INNER JOIN 確保只刪除真正匹配的項目
-- 這比使用 EXISTS 或 IN 更有效率
     matching_products AS (
         SELECT DISTINCT p.id, p.pid, p.product_vat, p.group_id, p.item_id, p.name
         FROM products p
                  INNER JOIN input_params ip ON (
             p.pid = ip.pid
                 AND p.product_vat = ip.product_vat
                 AND p.group_id = ip.group_id
                 AND p.item_id = ip.item_id
                 AND p.name = ip.name
             )
         WHERE p.is_deleted = false  -- 只刪除未被軟刪除的項目
     ),
-- 執行硬刪除並返回被刪除的項目
     deleted_products AS (
         DELETE FROM products
             WHERE id IN (SELECT id FROM matching_products)
             RETURNING id, pid, product_vat, group_id, item_id, name, category, created_at
     )
SELECT * FROM deleted_products
ORDER BY pid, item_id;  -- 排序便於後續處理