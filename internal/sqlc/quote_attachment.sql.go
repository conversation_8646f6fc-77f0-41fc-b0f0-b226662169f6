// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: quote_attachment.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

type BatchCreateQuoteAttachmentsParams struct {
	QuoteID    uint32 `json:"quoteId"`
	FilePath   string `json:"filePath"`
	FileName   string `json:"fileName"`
	FileSize   int32  `json:"fileSize"`
	FileType   string `json:"fileType"`
	UploadedBy uint32 `json:"uploadedBy"`
}

const countQuoteAttachmentsByQuoteID = `-- name: CountQuoteAttachmentsByQuoteID :one
SELECT COUNT(*) FROM quote_attachments
WHERE quote_id = $1
`

// 計算特定報價的附件數量
func (q *Queries) CountQuoteAttachmentsByQuoteID(ctx context.Context, quoteID uint32) (int64, error) {
	row := q.db.QueryRow(ctx, countQuoteAttachmentsByQuoteID, quoteID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createQuoteAttachment = `-- name: CreateQuoteAttachment :one
INSERT INTO quote_attachments (
    quote_id,
    file_path,
    file_name,
    file_size,
    file_type,
    uploaded_by
) VALUES (
             $1, $2, $3, $4, $5, $6
         )
RETURNING id, quote_id, file_path, file_name, file_size, file_type, file_hash, uploaded_at, uploaded_by, status, remark
`

type CreateQuoteAttachmentParams struct {
	QuoteID    uint32 `json:"quoteId"`
	FilePath   string `json:"filePath"`
	FileName   string `json:"fileName"`
	FileSize   int32  `json:"fileSize"`
	FileType   string `json:"fileType"`
	UploadedBy uint32 `json:"uploadedBy"`
}

// 創建一筆附件記錄
func (q *Queries) CreateQuoteAttachment(ctx context.Context, arg CreateQuoteAttachmentParams) (*QuoteAttachment, error) {
	row := q.db.QueryRow(ctx, createQuoteAttachment,
		arg.QuoteID,
		arg.FilePath,
		arg.FileName,
		arg.FileSize,
		arg.FileType,
		arg.UploadedBy,
	)
	var i QuoteAttachment
	err := row.Scan(
		&i.ID,
		&i.QuoteID,
		&i.FilePath,
		&i.FileName,
		&i.FileSize,
		&i.FileType,
		&i.FileHash,
		&i.UploadedAt,
		&i.UploadedBy,
		&i.Status,
		&i.Remark,
	)
	return &i, err
}

const createQuoteAttachmentWithTx = `-- name: CreateQuoteAttachmentWithTx :one
INSERT INTO quote_attachments (
    quote_id,
    file_path,
    file_name,
    file_size,
    file_type,
    uploaded_by
) VALUES (
             $1, $2, $3, $4, $5, $6
         )
RETURNING id, quote_id, file_path, file_name, file_size, file_type, file_hash, uploaded_at, uploaded_by, status, remark
`

type CreateQuoteAttachmentWithTxParams struct {
	QuoteID    uint32 `json:"quoteId"`
	FilePath   string `json:"filePath"`
	FileName   string `json:"fileName"`
	FileSize   int32  `json:"fileSize"`
	FileType   string `json:"fileType"`
	UploadedBy uint32 `json:"uploadedBy"`
}

// 在事務中創建一筆附件記錄
func (q *Queries) CreateQuoteAttachmentWithTx(ctx context.Context, arg CreateQuoteAttachmentWithTxParams) (*QuoteAttachment, error) {
	row := q.db.QueryRow(ctx, createQuoteAttachmentWithTx,
		arg.QuoteID,
		arg.FilePath,
		arg.FileName,
		arg.FileSize,
		arg.FileType,
		arg.UploadedBy,
	)
	var i QuoteAttachment
	err := row.Scan(
		&i.ID,
		&i.QuoteID,
		&i.FilePath,
		&i.FileName,
		&i.FileSize,
		&i.FileType,
		&i.FileHash,
		&i.UploadedAt,
		&i.UploadedBy,
		&i.Status,
		&i.Remark,
	)
	return &i, err
}

const deleteQuoteAttachment = `-- name: DeleteQuoteAttachment :exec
DELETE FROM quote_attachments
WHERE id = $1
`

// 刪除指定的附件記錄
func (q *Queries) DeleteQuoteAttachment(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteQuoteAttachment, id)
	return err
}

const deleteQuoteAttachmentsByQuoteID = `-- name: DeleteQuoteAttachmentsByQuoteID :exec
DELETE FROM quote_attachments
WHERE quote_id = $1
`

// 刪除指定報價的所有附件記錄
func (q *Queries) DeleteQuoteAttachmentsByQuoteID(ctx context.Context, quoteID uint32) error {
	_, err := q.db.Exec(ctx, deleteQuoteAttachmentsByQuoteID, quoteID)
	return err
}

const getProjectIdByAttachmentId = `-- name: GetProjectIdByAttachmentId :one
SELECT p.id AS project_id
FROM quote_attachments qa
         JOIN quotes q ON qa.quote_id = q.id
         JOIN projects p ON q.project_id = p.id
WHERE qa.id = $1
`

// 通過附件ID獲取專案ID，用於權限檢查
func (q *Queries) GetProjectIdByAttachmentId(ctx context.Context, id uint32) (uint32, error) {
	row := q.db.QueryRow(ctx, getProjectIdByAttachmentId, id)
	var project_id uint32
	err := row.Scan(&project_id)
	return project_id, err
}

const getQuoteAttachmentByID = `-- name: GetQuoteAttachmentByID :one
SELECT id, quote_id, file_path, file_name, file_size, file_type, file_hash, uploaded_at, uploaded_by, status, remark FROM quote_attachments
WHERE id = $1
`

// 根據ID獲取單筆附件檔案資訊
func (q *Queries) GetQuoteAttachmentByID(ctx context.Context, id uint32) (*QuoteAttachment, error) {
	row := q.db.QueryRow(ctx, getQuoteAttachmentByID, id)
	var i QuoteAttachment
	err := row.Scan(
		&i.ID,
		&i.QuoteID,
		&i.FilePath,
		&i.FileName,
		&i.FileSize,
		&i.FileType,
		&i.FileHash,
		&i.UploadedAt,
		&i.UploadedBy,
		&i.Status,
		&i.Remark,
	)
	return &i, err
}

const getQuoteAttachmentFilePath = `-- name: GetQuoteAttachmentFilePath :one
SELECT file_path, file_name, file_size FROM quote_attachments
WHERE id = $1
`

type GetQuoteAttachmentFilePathRow struct {
	FilePath string `json:"filePath"`
	FileName string `json:"fileName"`
	FileSize int32  `json:"fileSize"`
}

// 獲取附件的檔案路徑，用於檔案下載功能
func (q *Queries) GetQuoteAttachmentFilePath(ctx context.Context, id uint32) (*GetQuoteAttachmentFilePathRow, error) {
	row := q.db.QueryRow(ctx, getQuoteAttachmentFilePath, id)
	var i GetQuoteAttachmentFilePathRow
	err := row.Scan(&i.FilePath, &i.FileName, &i.FileSize)
	return &i, err
}

const listQuoteAttachmentsByQuoteID = `-- name: ListQuoteAttachmentsByQuoteID :many
SELECT id, quote_id, file_path, file_name, file_size, file_type, file_hash, uploaded_at, uploaded_by, status, remark FROM quote_attachments
WHERE quote_id = $1
ORDER BY uploaded_at DESC
`

// 根據報價ID獲取所有附件檔案資訊，按照上傳時間降序排序
func (q *Queries) ListQuoteAttachmentsByQuoteID(ctx context.Context, quoteID uint32) ([]*QuoteAttachment, error) {
	rows, err := q.db.Query(ctx, listQuoteAttachmentsByQuoteID, quoteID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*QuoteAttachment{}
	for rows.Next() {
		var i QuoteAttachment
		if err := rows.Scan(
			&i.ID,
			&i.QuoteID,
			&i.FilePath,
			&i.FileName,
			&i.FileSize,
			&i.FileType,
			&i.FileHash,
			&i.UploadedAt,
			&i.UploadedBy,
			&i.Status,
			&i.Remark,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listQuoteAttachmentsWithFilters = `-- name: ListQuoteAttachmentsWithFilters :many
SELECT id, quote_id, file_path, file_name, file_size, file_type, file_hash, uploaded_at, uploaded_by, status, remark FROM quote_attachments
WHERE
  -- 使用COALESCE處理空值過濾條件
    (COALESCE($1::INTEGER, 0) = 0 OR quote_id = $1::INTEGER) AND
    ($2::VARCHAR = '' OR file_type = $2::VARCHAR) AND
    (COALESCE($3::INTEGER, 0) = 0 OR uploaded_by = $3::INTEGER) AND
    ($4::TIMESTAMPTZ IS NULL OR uploaded_at >= $4::TIMESTAMPTZ) AND
    ($5::TIMESTAMPTZ IS NULL OR uploaded_at <= $5::TIMESTAMPTZ) AND
    ($6::VARCHAR = '' OR file_name ILIKE '%' || $6::VARCHAR || '%')
ORDER BY
    CASE
        WHEN $7::TEXT = 'uploaded_at' AND $8::TEXT = 'asc' THEN uploaded_at
        END ASC NULLS LAST,
    CASE
        WHEN $7::TEXT = 'uploaded_at' AND $8::TEXT = 'desc' THEN uploaded_at
        END DESC NULLS LAST,
    CASE
        WHEN $7::TEXT = 'file_size' AND $8::TEXT = 'asc' THEN file_size
        END ASC NULLS LAST,
    CASE
        WHEN $7::TEXT = 'file_size' AND $8::TEXT = 'desc' THEN file_size
        END DESC NULLS LAST,
    -- 預設排序
    CASE
        WHEN $7::TEXT = '' OR $7::TEXT IS NULL THEN uploaded_at
        END DESC
LIMIT COALESCE($10::INTEGER, 50)
    OFFSET COALESCE($9::INTEGER, 0)
`

type ListQuoteAttachmentsWithFiltersParams struct {
	QuoteID        *int32             `json:"quoteId"`
	FileType       *string            `json:"fileType"`
	UploadedBy     *int32             `json:"uploadedBy"`
	UploadedAfter  pgtype.Timestamptz `json:"uploadedAfter"`
	UploadedBefore pgtype.Timestamptz `json:"uploadedBefore"`
	FileNameSearch *string            `json:"fileNameSearch"`
	SortBy         *string            `json:"sortBy"`
	SortDir        *string            `json:"sortDir"`
	Offset         *int32             `json:"offset"`
	Limit          *int32             `json:"limit"`
}

// 根據多種條件查詢附件記錄
// 使用動態排序
// 分頁參數
func (q *Queries) ListQuoteAttachmentsWithFilters(ctx context.Context, arg ListQuoteAttachmentsWithFiltersParams) ([]*QuoteAttachment, error) {
	rows, err := q.db.Query(ctx, listQuoteAttachmentsWithFilters,
		arg.QuoteID,
		arg.FileType,
		arg.UploadedBy,
		arg.UploadedAfter,
		arg.UploadedBefore,
		arg.FileNameSearch,
		arg.SortBy,
		arg.SortDir,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*QuoteAttachment{}
	for rows.Next() {
		var i QuoteAttachment
		if err := rows.Scan(
			&i.ID,
			&i.QuoteID,
			&i.FilePath,
			&i.FileName,
			&i.FileSize,
			&i.FileType,
			&i.FileHash,
			&i.UploadedAt,
			&i.UploadedBy,
			&i.Status,
			&i.Remark,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateQuoteAttachment = `-- name: UpdateQuoteAttachment :one
UPDATE quote_attachments
SET
    file_name = COALESCE($1, file_name),
    file_type = COALESCE($2, file_type)
WHERE id = $3
RETURNING id, quote_id, file_path, file_name, file_size, file_type, file_hash, uploaded_at, uploaded_by, status, remark
`

type UpdateQuoteAttachmentParams struct {
	FileName *string `json:"fileName"`
	FileType *string `json:"fileType"`
	ID       uint32  `json:"id"`
}

// 更新附件記錄（有些欄位如檔案路徑、大小等通常不會更新，但為API完整性提供）
func (q *Queries) UpdateQuoteAttachment(ctx context.Context, arg UpdateQuoteAttachmentParams) (*QuoteAttachment, error) {
	row := q.db.QueryRow(ctx, updateQuoteAttachment, arg.FileName, arg.FileType, arg.ID)
	var i QuoteAttachment
	err := row.Scan(
		&i.ID,
		&i.QuoteID,
		&i.FilePath,
		&i.FileName,
		&i.FileSize,
		&i.FileType,
		&i.FileHash,
		&i.UploadedAt,
		&i.UploadedBy,
		&i.Status,
		&i.Remark,
	)
	return &i, err
}
