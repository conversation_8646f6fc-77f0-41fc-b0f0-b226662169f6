// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: passwordreset.sql

package sqlc

import (
	"context"
	"time"
)

const cleanAllPasswordResetRequests = `-- name: CleanAllPasswordResetRequests :exec

/*
   清除所有密碼重設請求
   - 使用場景: 系統維護或重置操作
   - 安全考慮: 此操作應受嚴格權限控制，且僅在特殊情況下使用
   - 建議: 在生產環境中應謹慎使用此操作
*/
DELETE FROM password_reset_requests
WHERE TRUE
`

// 刪除已使用且超過30天的記錄
func (q *Queries) CleanAllPasswordResetRequests(ctx context.Context) error {
	_, err := q.db.Exec(ctx, cleanAllPasswordResetRequests)
	return err
}

const cleanExpiredPasswordResetRequests = `-- name: CleanExpiredPasswordResetRequests :exec

/*
   清理已過期的密碼重設請求
   - 使用場景: 定期維護任務，移除系統中過期的重設請求以保持資料庫乾淨
   - 效能考慮: 建議在低峰期執行此操作，或對大型資料庫添加LIMIT子句分批刪除
*/
DELETE FROM password_reset_requests
WHERE expires_at < CURRENT_TIMESTAMP
  OR (is_used = TRUE AND created_at < (CURRENT_TIMESTAMP - INTERVAL '30 days'))
`

// 確保只更新未使用的請求
func (q *Queries) CleanExpiredPasswordResetRequests(ctx context.Context) error {
	_, err := q.db.Exec(ctx, cleanExpiredPasswordResetRequests)
	return err
}

const createPasswordResetRequest = `-- name: CreatePasswordResetRequest :one
/*
   創建新的密碼重設請求
   - 使用場景: 用戶請求重設密碼時生成新的重設 token 
   - 索引建議: user_id欄位應該有索引以支持快速查找特定用戶的請求
*/
INSERT INTO password_reset_requests (
    user_id,
    token,
    expires_at,
    created_by
) VALUES (
    $1, $2, $3, $4
)
RETURNING
    id, user_id, token, is_used, expires_at, created_at, created_by
`

type CreatePasswordResetRequestParams struct {
	UserID    uint32    `json:"userId"`
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expiresAt"`
	CreatedBy uint32    `json:"createdBy"`
}

type CreatePasswordResetRequestRow struct {
	ID        uint32    `json:"id"`
	UserID    uint32    `json:"userId"`
	Token     string    `json:"token"`
	IsUsed    bool      `json:"isUsed"`
	ExpiresAt time.Time `json:"expiresAt"`
	CreatedAt time.Time `json:"createdAt"`
	CreatedBy uint32    `json:"createdBy"`
}

func (q *Queries) CreatePasswordResetRequest(ctx context.Context, arg CreatePasswordResetRequestParams) (*CreatePasswordResetRequestRow, error) {
	row := q.db.QueryRow(ctx, createPasswordResetRequest,
		arg.UserID,
		arg.Token,
		arg.ExpiresAt,
		arg.CreatedBy,
	)
	var i CreatePasswordResetRequestRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Token,
		&i.IsUsed,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.CreatedBy,
	)
	return &i, err
}

const getActivePasswordResetRequestsByUserID = `-- name: GetActivePasswordResetRequestsByUserID :many
/*
   查詢用戶的有效密碼重設請求
   - 使用場景: 檢查用戶是否已有進行中的重設請求，避免短時間內發送多個重設郵件
   - 安全考慮: 可用於檢測異常的密碼重設行為
*/
SELECT
    id,
    user_id,
    token,
    is_used,
    expires_at,
    created_at,
    created_by
FROM password_reset_requests
WHERE user_id = $1
  AND is_used = FALSE
  AND expires_at > CURRENT_TIMESTAMP
ORDER BY created_at DESC
`

type GetActivePasswordResetRequestsByUserIDRow struct {
	ID        uint32    `json:"id"`
	UserID    uint32    `json:"userId"`
	Token     string    `json:"token"`
	IsUsed    bool      `json:"isUsed"`
	ExpiresAt time.Time `json:"expiresAt"`
	CreatedAt time.Time `json:"createdAt"`
	CreatedBy uint32    `json:"createdBy"`
}

func (q *Queries) GetActivePasswordResetRequestsByUserID(ctx context.Context, userID uint32) ([]*GetActivePasswordResetRequestsByUserIDRow, error) {
	rows, err := q.db.Query(ctx, getActivePasswordResetRequestsByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetActivePasswordResetRequestsByUserIDRow{}
	for rows.Next() {
		var i GetActivePasswordResetRequestsByUserIDRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Token,
			&i.IsUsed,
			&i.ExpiresAt,
			&i.CreatedAt,
			&i.CreatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPasswordResetRequestByToken = `-- name: GetPasswordResetRequestByToken :one
/*
   根據重設密碼 token 獲取重設請求詳情
   - 優化建議: 為token欄位添加唯一索引以加速查詢
   - 使用場景: 用戶點擊重設密碼郵件中的連結時驗證 token 
*/
SELECT
    id,
    user_id,
    token,
    is_used,
    expires_at,
    created_at,
    created_by
FROM password_reset_requests
WHERE token = $1
  AND is_used = FALSE  -- 只檢索未使用的 token 
  AND expires_at > CURRENT_TIMESTAMP  -- 只檢索未過期的 token 
LIMIT 1
`

type GetPasswordResetRequestByTokenRow struct {
	ID        uint32    `json:"id"`
	UserID    uint32    `json:"userId"`
	Token     string    `json:"token"`
	IsUsed    bool      `json:"isUsed"`
	ExpiresAt time.Time `json:"expiresAt"`
	CreatedAt time.Time `json:"createdAt"`
	CreatedBy uint32    `json:"createdBy"`
}

func (q *Queries) GetPasswordResetRequestByToken(ctx context.Context, token string) (*GetPasswordResetRequestByTokenRow, error) {
	row := q.db.QueryRow(ctx, getPasswordResetRequestByToken, token)
	var i GetPasswordResetRequestByTokenRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Token,
		&i.IsUsed,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.CreatedBy,
	)
	return &i, err
}

const markPasswordResetRequestAsUsed = `-- name: MarkPasswordResetRequestAsUsed :exec
/*
   標記密碼重設請求為已使用
   - 使用場景: 用戶成功重設密碼後，防止 token 被再次使用
   - 添加更新時間追蹤以便審計
*/
UPDATE password_reset_requests
SET
    is_used = TRUE,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
  AND is_used = FALSE
`

func (q *Queries) MarkPasswordResetRequestAsUsed(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, markPasswordResetRequestAsUsed, id)
	return err
}
