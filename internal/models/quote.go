package models

import (
	"path/filepath"
	"strings"
	"time"

	"github.com/shopspring/decimal"

	"pms-api/internal/sqlc"
)

// Quote 代表軟體品項的報價資訊。
// 包含廠商報價、軟協報價和辦公室報價三種類型，每種報價有不同的審核流程。
type Quote struct {

	// CreatedAt 是報價創建時間。
	// 用於記錄報價首次提交的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是報價最後更新時間。
	// 用於記錄報價最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// ReviewedAt 是報價審核時間，可為空。
	// 用於記錄報價被審核的時間，只有在報價被審核後才會有值。
	ReviewedAt *time.Time `json:"reviewed_at,omitempty"`

	// MarketPrice 是市售價，可為空。
	// 使用 decimal 確保財務計算精確，精度為 20 位整數部分，2 位小數部分。
	// 範圍：大於等於 0。
	MarketPrice *decimal.Decimal `json:"market_price,omitempty" validate:"omitempty,gte=0"`

	// InternetPrice 是網路價，可為空。
	// 使用 decimal 確保財務計算精確，精度為 20 位整數部分，2 位小數部分。
	// 範圍：大於等於 0。
	InternetPrice *decimal.Decimal `json:"internet_price,omitempty" validate:"omitempty,gte=0"`

	// OriginalPrice 是原廠價，可為空。
	// 使用 decimal 確保財務計算精確，精度為 20 位整數部分，2 位小數部分。
	// 範圍：大於等於 0。
	OriginalPrice *decimal.Decimal `json:"original_price,omitempty" validate:"omitempty,gte=0"`

	// PromotionPrice 是促銷價，可為空。
	// 使用 decimal 確保財務計算精確，精度為 20 位整數部分，2 位小數部分。
	// 範圍：大於等於 0。
	PromotionPrice *decimal.Decimal `json:"promotion_price,omitempty" validate:"omitempty,gte=0"`

	// BidPrice 是決標價，可為空。
	// 使用 decimal 確保財務計算精確，精度為 20 位整數部分，2 位小數部分。
	// 範圍：大於等於 0。僅用於定期詢價。
	BidPrice *decimal.Decimal `json:"bid_price,omitempty" validate:"omitempty,gte=0"`

	// QuoteType 是報價類型，只能是「廠商報價」、「軟協報價」或「辦公室報價」。
	// 非空，用於區分不同來源的報價。
	QuoteType sqlc.QuoteType `json:"quote_type" validate:"required,oneof=廠商報價 軟協報價 辦公室報價"`

	// Status 是報價狀態，只能是「待審」、「通過」、「退件」或「重送」。
	// 非空，預設為「待審」，用於追蹤報價的審核流程狀態。
	Status sqlc.QuoteStatus `json:"status" validate:"required,oneof=待審 通過 退件 重送"`

	// Remark 是報價備註，可為空。
	// 用於記錄報價的補充說明，例如特殊折扣條件或使用限制。
	Remark string `json:"remark,omitempty"`

	// AdminRemark 是管理員備註，可為空。
	// 用於記錄審核意見或退件原因，只有管理員可見。
	AdminRemark string `json:"admin_remark,omitempty"`

	// ID 是報價的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表。
	// 非空，用於識別報價所屬的詢價專案，範圍：1 到 4294967295。
	ProjectID uint32 `json:"project_id" validate:"required,min=1"`

	// ProductID 是關聯產品的 ID，外鍵指向 products 表。
	// 非空，用於識別報價的產品品項，範圍：1 到 4294967295。
	ProductID uint32 `json:"product_id" validate:"required,min=1"`

	// UserID 是關聯用戶的 ID，外鍵指向 users 表。
	// 非空，用於識別提交報價的用戶，範圍：1 到 4294967295。
	UserID uint32 `json:"user_id" validate:"required,min=1"`

	// ReviewedBy 是審核報價的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰審核了此報價，若未填寫則為 0，範圍：0 到 4294967295。
	ReviewedBy *uint32 `json:"reviewed_by,omitempty"`

	// SameAsBidPrice 表示報價是否與決標價相同，可為空。
	// 僅用於定期詢價，若為 true 則表示報價與決標價相同。
	SameAsBidPrice *bool `json:"same_as_bid_price,omitempty"`

	// IsDeleted 表示報價是否已被邏輯刪除。
	// 預設為 false，若為 true 則表示此報價已被標記為刪除，但仍保留在資料庫中。
	IsDeleted bool `json:"is_deleted"`
}

// IsApproved 判斷報價是否已通過審核。
func (q *Quote) IsApproved() bool {
	return q.Status == sqlc.QuoteStatusValue1
}

// IsPending 判斷報價是否待審核。
func (q *Quote) IsPending() bool {
	return q.Status == sqlc.QuoteStatusValue0
}

// IsRejected 判斷報價是否被退件。
func (q *Quote) IsRejected() bool {
	return q.Status == sqlc.QuoteStatusValue2
}

// IsResent 判斷報價是否為重送。
func (q *Quote) IsResent() bool {
	return q.Status == sqlc.QuoteStatusValue3
}

// GetLowestPrice 獲取報價中的最低價格（不包括促銷價）。
// 如果沒有任何價格資訊，則返回 nil。
func (q *Quote) GetLowestPrice() *decimal.Decimal {
	var prices []*decimal.Decimal
	if q.MarketPrice != nil {
		prices = append(prices, q.MarketPrice)
	}
	if q.InternetPrice != nil {
		prices = append(prices, q.InternetPrice)
	}
	if q.OriginalPrice != nil {
		prices = append(prices, q.OriginalPrice)
	}
	if q.BidPrice != nil {
		prices = append(prices, q.BidPrice)
	}

	if len(prices) == 0 {
		return nil
	}

	lowestPrice := prices[0]
	for _, price := range prices[1:] {
		if price.LessThan(*lowestPrice) {
			lowestPrice = price
		}
	}
	return lowestPrice
}

// IsValid 檢查報價是否有效。
// 至少需要填寫一個非促銷價格（市售價、網路價、原廠價或決標價）。
func (q *Quote) IsValid() bool {
	
	return q.MarketPrice != nil || q.InternetPrice != nil || q.OriginalPrice != nil || q.BidPrice != nil
}

// QuoteAttachment 代表報價的佐證附件。
// 用於儲存報價相關的支援文件，如報價單、產品規格或其他佐證資料。
type QuoteAttachment struct {

	// UploadedAt 是附件上傳時間。
	// 用於記錄附件上傳的時間，通常用於排序或日誌追蹤。
	UploadedAt time.Time `json:"uploaded_at"`

	// FilePath 是附件的檔案路徑。
	// 非空，最大長度 255 字元，表示附件在儲存系統中的實際位置。
	// 格式通常為「/uploads/quotes/{project_id}/{quote_id}/{timestamp}_{filename}」。
	FilePath string `json:"file_path" validate:"required,max=255"`

	// FileName 是附件的原始檔案名稱。
	// 非空，最大長度 255 字元，表示上傳時的原始檔案名稱。
	FileName string `json:"file_name" validate:"required,max=255"`

	// FileType 是附件的檔案類型。
	// 非空，最大長度 50 字元，表示檔案的 MIME 類型或副檔名。
	// 限制為 jpeg/jpg/png/pdf/doc/docx 格式。
	FileType string `json:"file_type" validate:"required,max=50"`

	// ID 是附件的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// QuoteID 是關聯報價的 ID，外鍵指向 quotes 表。
	// 非空，用於識別附件所屬的報價，範圍：1 到 4294967295。
	QuoteID uint32 `json:"quote_id" validate:"required,min=1"`

	// FileSize 是附件的檔案大小，以位元組為單位。
	// 非空，用於追蹤附件的儲存空間使用情況。
	FileSize int32 `json:"file_size" validate:"required,min=1"`

	// UploadedBy 是上傳附件的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰上傳了此附件，若未填寫則為 0，範圍：0 到 4294967295。
	UploadedBy *uint32 `json:"uploaded_by,omitempty"`
}

// GetExtension 獲取附件的副檔名（不含點號）。
func (qa *QuoteAttachment) GetExtension() string {
	return strings.TrimPrefix(filepath.Ext(qa.FileName), ".")
}

// IsImage 判斷附件是否為圖片檔案。
func (qa *QuoteAttachment) IsImage() bool {
	ext := strings.ToLower(qa.GetExtension())
	return ext == "jpg" || ext == "jpeg" || ext == "png"
}

// IsPDF 判斷附件是否為 PDF 檔案。
func (qa *QuoteAttachment) IsPDF() bool {
	ext := strings.ToLower(qa.GetExtension())
	return ext == "pdf"
}

// IsDocument 判斷附件是否為文件檔案（Word）。
func (qa *QuoteAttachment) IsDocument() bool {
	ext := strings.ToLower(qa.GetExtension())
	return ext == "doc" || ext == "docx"
}

// IsValidFileType 判斷檔案類型是否有效。
// 只允許 jpeg/jpg/png/pdf/doc/docx 格式。
func (qa *QuoteAttachment) IsValidFileType() bool {
	return qa.IsImage() || qa.IsPDF() || qa.IsDocument()
}

// QuoteStats 代表報價統計資訊。
// 用於記錄專案中報價的統計資訊。
type QuoteStats struct {

	// TotalCount 是總報價數量。
	// 記錄專案中的總報價數量。
	TotalCount int `json:"total_count"`

	// VendorQuoteCount 是廠商報價數量。
	// 記錄專案中的廠商報價數量。
	VendorQuoteCount int `json:"vendor_quote_count"`

	// CISAQuoteCount 是軟協報價數量。
	// 記錄專案中的軟協報價數量。
	CISAQuoteCount int `json:"cisa_quote_count"`

	// SPOQuoteCount 是辦公室報價數量。
	// 記錄專案中的辦公室報價數量。
	SPOQuoteCount int `json:"spo_quote_count"`

	// PendingCount 是待審核報價數量。
	// 記錄專案中的待審核報價數量。
	PendingCount int `json:"pending_count"`

	// ApprovedCount 是已通過報價數量。
	// 記錄專案中的已通過報價數量。
	ApprovedCount int `json:"approved_count"`

	// RejectedCount 是已退件報價數量。
	// 記錄專案中的已退件報價數量。
	RejectedCount int `json:"rejected_count"`

	// ResubmittedCount 是已重送報價數量。
	// 記錄專案中的已重送報價數量。
	ResubmittedCount int `json:"resubmitted_count"`

	// VendorCount 是提交報價的廠商數量。
	// 記錄參與報價的不同廠商數量。
	VendorCount int `json:"vendor_count"`

	// ProductCount 是有報價的產品數量。
	// 記錄有報價的產品數量。
	ProductCount int `json:"product_count"`

	// WithAttachmentCount 是有附件的報價數量。
	// 記錄有上傳佐證附件的報價數量。
	WithAttachmentCount int `json:"with_attachment_count"`
}

// BatchQuoteRejection 代表批量退件記錄。
// 用於追蹤批量退件操作的執行情況，如退件原因、影響的報價數量等。
type BatchQuoteRejection struct {

	// CreatedAt 是記錄創建時間。
	// 非空，記錄批量退件操作的執行時間。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是記錄最後更新時間。
	// 非空，記錄批量退件資料最後修改的時間。
	UpdatedAt time.Time `json:"updated_at"`

	// Reason 是退件原因，可為空。
	// 用於記錄批量退件的原因或說明。
	Reason string `json:"reason,omitempty"`

	// ID 是記錄的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表。
	// 非空，用於識別批量退件所屬的詢價專案，範圍：1 到 4294967295。
	ProjectID uint32 `json:"project_id" validate:"required,min=1"`

	// AffectedCount 是影響的報價數量。
	// 非空，記錄批量退件影響的報價數量，預設為 0，範圍：0 到 4294967295。
	AffectedCount uint32 `json:"affected_count" validate:"min=0"`

	// CreatedBy 是創建記錄的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰執行了此批量退件操作，若未填寫則為 0，範圍：0 到 4294967295。
	CreatedBy *uint32 `json:"created_by,omitempty"`
}

// HasReason 判斷是否有退件原因。
func (bqr *BatchQuoteRejection) HasReason() bool {
	return bqr.Reason != ""
}

// HasAffectedQuotes 判斷是否影響了任何報價。
func (bqr *BatchQuoteRejection) HasAffectedQuotes() bool {
	return bqr.AffectedCount > 0
}

// QuoteApprovalHistory 代表報價審核歷史紀錄。
// 用於追蹤報價狀態的變更歷程，包含審核意見和變更原因。
type QuoteApprovalHistory struct {

	// CreatedAt 是審核紀錄創建時間。
	// 用於記錄狀態變更的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// OldStatus 是變更前的報價狀態。
	// 非空，表示變更前的報價狀態，例如「待審」、「通過」、「退件」或「重送」。
	OldStatus sqlc.QuoteStatus `json:"old_status" validate:"required,oneof=待審 通過 退件 重送"`

	// NewStatus 是變更後的報價狀態。
	// 非空，表示變更後的報價狀態，例如「待審」、「通過」、「退件」或「重送」。
	NewStatus sqlc.QuoteStatus `json:"new_status" validate:"required,oneof=待審 通過 退件 重送"`

	// Remark 是審核備註，可為空。
	// 用於記錄審核的補充說明或變更原因，例如退件原因或特殊說明。
	Remark string `json:"remark,omitempty"`

	// ID 是審核紀錄的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// QuoteID 是關聯報價的 ID，外鍵指向 quotes 表。
	// 非空，用於識別審核紀錄所屬的報價，範圍：1 到 4294967295。
	QuoteID uint32 `json:"quote_id" validate:"required,min=1"`

	// CreatedBy 是創建審核紀錄的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰進行了此次審核，若未填寫則為 0，範圍：0 到 4294967295。
	CreatedBy *uint32 `json:"created_by,omitempty"`

	// BatchID 是批次審核 ID，外鍵指向 batch_quote_rejections 表，可為空。
	// 用於識別此審核是否為批次審核的一部分，若未填寫則為 0。
	BatchID *uint32 `json:"batch_id,omitempty"`
}

// IsApproval 判斷是否為核准操作。
func (qah *QuoteApprovalHistory) IsApproval() bool {
	return qah.NewStatus == sqlc.QuoteStatusValue1 && qah.OldStatus != sqlc.QuoteStatusValue1
}

// IsRejection 判斷是否為退件操作。
func (qah *QuoteApprovalHistory) IsRejection() bool {
	return qah.NewStatus == sqlc.QuoteStatusValue2 && qah.OldStatus != sqlc.QuoteStatusValue2
}

// IsResubmission 判斷是否為重送操作。
func (qah *QuoteApprovalHistory) IsResubmission() bool {
	return qah.NewStatus == sqlc.QuoteStatusValue3 && qah.OldStatus != sqlc.QuoteStatusValue3
}

// IsBatchOperation 判斷是否為批次操作。
func (qah *QuoteApprovalHistory) IsBatchOperation() bool {
	return qah.BatchID != nil && *qah.BatchID > 0
}

// QuoteDetail 代表報價詳細資訊。
// 包含報價基本資料、產品資訊、用戶資訊、附件等綜合資訊，用於前端顯示和管理。
type QuoteDetail struct {

	// Quote 是報價基本資料。
	// 包含 ID、報價類型、各種價格、狀態等基本資訊。
	Quote *Quote `json:"quote"`

	// Product 是報價的產品資訊。
	// 包含產品 ID、名稱、項次、類別等資訊。
	Product *Product `json:"product"`

	// ProductGroup 是產品所屬組別資訊。
	// 包含組別 ID、編號、名稱等資訊。
	ProductGroup *ProductGroup `json:"product_group"`

	// User 是提交報價的用戶資訊。
	// 包含用戶 ID、帳號名稱、類型等資訊。
	User *User `json:"user"`

	// Company 是提交報價的廠商資訊，可為空。
	// 當報價類型為「廠商報價」時，包含廠商 ID、名稱、統一編號等資訊。
	Company *Company `json:"company,omitempty"`

	// Attachments 是報價附件列表。
	// 包含附件 ID、檔案路徑、檔案名稱等資訊。
	Attachments []*QuoteAttachment `json:"attachments,omitempty"`

	// ApprovalHistory 是報價審核歷史列表。
	// 包含審核歷史 ID、狀態變更、備註等資訊。
	ApprovalHistory []*QuoteApprovalHistory `json:"approval_history,omitempty"`

	// ReviewedByUser 是審核者資訊，可為空。
	// 記錄審核報價的用戶資訊。
	ReviewedByUser *User `json:"reviewed_by_user,omitempty"`

	// LowestPrice 是最低價格，可為空。
	// 報價中除促銷價外的最低價格。
	LowestPrice *decimal.Decimal `json:"lowest_price,omitempty"`

	// IsContractedVendor 表示是否為立約商。
	// 用於判斷廠商是否為該產品的立約商，僅在定期詢價中有意義。
	IsContractedVendor bool `json:"is_contracted_vendor"`

	// CanCorrect 表示是否可以補正。
	// 根據報價狀態和當前時間範圍判斷是否允許補正操作。
	CanCorrect bool `json:"can_correct"`

	// ProjectName 是專案名稱。
	// 記錄報價所屬的專案名稱，便於前端顯示。
	ProjectName string `json:"project_name"`
}

// QuoteListParams 基礎報價過濾條件
// 包含所有報價查詢可能用到的通用過濾條件
type QuoteListParams struct {

	// 報價狀態，可篩選多個狀態
	Status *sqlc.QuoteStatus `json:"status,omitempty"`

	// 報價類型，如廠商報價、軟協報價、辦公室報價
	QuoteType *sqlc.QuoteType `json:"quote_type,omitempty"`

	ProjectID *uint32 `json:"project_id,omitempty"`

	ProductID *uint32 `json:"product_id,omitempty"`

	UserID *uint32 `json:"user_id,omitempty"`

	CompanyID *uint32 `json:"company_id,omitempty"`

	GroupID *uint32 `json:"group_id,omitempty"`

	// 搜索關鍵字，用於模糊匹配 productName, itemNo, username, companyName
	SearchTerm *string `json:"search_term,omitempty"`

	// 排序依據，如 created_at, updated_at, reviewed_at
	SortBy *string `json:"sort_by,omitempty"`

	// 排序方向，如 asc, desc
	SortDirection *string `json:"sort_direction,omitempty"`
}

// ProjectQuoteFilter 專案報價過濾條件
// 用於 ListProjectQuotes 方法，包含專案內報價查詢的特定過濾條件
type ProjectQuoteFilter struct {
	QuoteListParams

	// 產品組別 ID，用於篩選特定組別的報價
	GroupID *uint32 `json:"group_id,omitempty"`

	// 項次，用於篩選特定項次的報價
	ItemNo *string `json:"item_no,omitempty"`

	// 產品類別，如原品項或新增品項
	ProductCategory *sqlc.ProductCategory `json:"product_category,omitempty"`

	// 產品 ID，用於篩選特定產品的報價
	ProductID *uint32 `json:"product_id,omitempty"`

	// 廠牌名稱，用於篩選特定廠牌的報價
	BrandName *string `json:"brand_name,omitempty"`

	// 品項名稱，支持模糊查詢
	ProductName *string `json:"product_name,omitempty"`

	// 報價性質，如新品或非新品
	QuoteNature *string `json:"quote_nature,omitempty"`

	// 僅包含獨家報價（只有一個廠商報價）
	OnlyExclusiveQuotes bool `json:"only_exclusive_quotes,omitempty"`

	// 報價類別篩選 (已報價/未被報價/未被廠商報價/廠商報價/軟協報價/辦公室報價)
	QuoteCategory *string `json:"quote_category,omitempty"`
}

// UserQuoteFilter 用戶報價過濾條件
// 用於 ListUserQuotes 方法，包含用戶報價查詢的特定過濾條件
type UserQuoteFilter struct {
	QuoteListParams

	// 項次，用於篩選特定項次的報價
	ItemNo *string `json:"item_no,omitempty"`

	// 品項名稱，支持模糊查詢
	ProductName *string `json:"product_name,omitempty"`

	// 產品 ID，用於篩選特定產品的報價
	ProductID *uint32 `json:"product_id,omitempty"`
}

// CompanyQuoteFilter 廠商報價過濾條件
// 用於 ListCompanyQuotes 方法，包含廠商報價查詢的特定過濾條件
type CompanyQuoteFilter struct {
	QuoteListParams

	// 項次，用於篩選特定項次的報價
	ItemNo *string `json:"item_no,omitempty"`

	// 品項名稱，支持模糊查詢
	ProductName *string `json:"product_name,omitempty"`

	// 產品 ID，用於篩選特定產品的報價
	ProductID *uint32 `json:"product_id,omitempty"`

	// 是否只查詢立約商品項
	OnlyContractedItems bool `json:"only_contracted_items,omitempty"`

	// 同決標價篩選
	SameAsBidPrice *bool `json:"same_as_bid_price,omitempty"`
}

// PendingQuoteFilter 待審核報價過濾條件
// 用於 ListPendingQuotes 方法，包含待審核報價查詢的特定過濾條件
//type PendingQuoteFilter struct {
//	// 廠牌名稱，用於篩選特定廠牌的報價
//	BrandName *string `json:"brand_name,omitempty"`
//
//	// 品項名稱，支持模糊查詢
//	ProductName *string `json:"product_name,omitempty"`
//
//	// 報價類型，如廠商報價、軟協報價
//	QuoteType *sqlc.QuoteType `json:"quote_type,omitempty"`
//
//	// 是否只包含「重送」狀態的報價
//	OnlyResubmitted bool `json:"only_resubmitted,omitempty"`
//
//	// 廠商統一編號，用於篩選特定廠商的報價
//	UnifiedBusinessNo *string `json:"unified_business_no,omitempty"`
//
//	// 廠商名稱，支持模糊查詢
//	CompanyName *string `json:"company_name,omitempty"`
//}

// ApprovalHistoryFilter 定義查詢審核歷史時的過濾條件
type ApprovalHistoryFilter struct {
	QuoteID       *uint32           `json:"quote_id" validate:"omitempty,min=1"`
	BatchID       *uint32           `json:"batch_id" validate:"omitempty,min=1"`
	CreatedBy     *uint32           `json:"created_by" validate:"omitempty,min=1"`
	OldStatus     *sqlc.QuoteStatus `json:"old_status" validate:"omitempty,oneof=待審 通過 退件 重送"`
	NewStatus     *sqlc.QuoteStatus `json:"new_status" validate:"omitempty,oneof=待審 通過 退件 重送"`
	CreatedAfter  *time.Time        `json:"created_after"`
	CreatedBefore *time.Time        `json:"created_before"`
}
